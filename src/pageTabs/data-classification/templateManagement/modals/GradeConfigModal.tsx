import React, { useEffect, useMemo } from 'react';
import { Form, Cascader, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { useRequest, useSelector } from 'src/hook';
import { UIModal } from 'src/components';
import {
  getClassBindCls,
  getClassDatasourceAPI,
  getClassNewTemplateAPI
} from 'src/api';
interface TemplateFormValues {
  templateName: string;
  tagType?: string;
  relatedTemplate?: string;
}

interface ClassifyLevelResponse {
  code: number;
  message: string;
  datas: {
    columns: string[];
    index: number[];
    data: [string, string][];
  };
}

const GradeConfigModal = ({
  onCancel,
  onRefreshTree
}: {
  onCancel: () => void;
  onRefreshTree?: () => void;
}) => {

  const {t }= useTranslation();
  const [templateForm] = Form.useForm<TemplateFormValues>();

  // 获取当前选中的节点信息
  const { selectedTemplateNodeInfo } = useSelector(state => state.dataClassification);

  // 获取完整的模板列表数据，用于查找根节点信息
  const { data: templateListData } = useRequest(() => {
    return getClassNewTemplateAPI({
      action: 'list_templates',
      limit: 1000000
    });
  }, {
    formatResult: (res: any) => {
      return res?.datas || [];
    }
  });

  // 获取根模板节点信息
  const rootTemplateNode = useMemo(() => {
    if (!selectedTemplateNodeInfo) return null;
    
    // 如果当前节点本身就是根节点，直接返回
    if (selectedTemplateNodeInfo.isRoot) {
      return selectedTemplateNodeInfo;
    }
    
    // 对于分类节点，从模板列表中查找对应的根模板信息
    if (selectedTemplateNodeInfo.tp_id && templateListData?.length) {
      const rootTemplate = templateListData.find((template: any) => 
        template.tp_id === selectedTemplateNodeInfo.tp_id
      );
      return rootTemplate || null;
    }
    
    // 如果没有可用数据，返回 null，这种情况下会显示所有级别选项
    return null;
  }, [selectedTemplateNodeInfo, templateListData]);

  //绑定分级
  const {loading ,run: runBindClsLevel } = useRequest(getClassBindCls, {
    manual: true,
    onSuccess: () => {
      message.success(t('common.message.edit.success'))
      onCancel();
      // 绑定等级配置成功后，刷新树数据以更新右侧显示
      if (onRefreshTree) {
        onRefreshTree();
      } else {
      }
    }
  })
  const { data: allLevelOptions, run: runGetClassifyLevel } = useRequest(getClassDatasourceAPI,{
    manual: true,
    formatResult: (res: ClassifyLevelResponse) => {
      if (res?.datas?.data && res?.datas?.index) {
        // 将扁平数据转换为树形结构，同时保存index映射
        const groupedData: Record<string, Array<{levelName: string, indexValue: number}>> = {};
        
        res.datas.data.forEach(([tpName, levelName]: [string, string], dataIndex: number) => {
          const indexValue = res.datas.index[dataIndex];
          if (!groupedData[tpName]) {
            groupedData[tpName] = [];
          }
          groupedData[tpName].push({ levelName, indexValue });
        });

        // 转换为Cascader需要的格式
        return Object.entries(groupedData).map(([tpName, levels]) => ({
          label: tpName,
          value: tpName,
          children: levels.map(({ levelName, indexValue }) => ({
            label: levelName,
            value: indexValue
          }))
        }));
      }
      return [];
    }
  })

  // 根据根模板节点的 level_type 过滤级别选项
  const levelOptions = useMemo(() => {
    if (!allLevelOptions || !rootTemplateNode) {
      return allLevelOptions || [];
    }

    const { level_type } = rootTemplateNode;
    
    // 如果是"自定义"，显示所有选项
    if (level_type === "自定义") {
      return allLevelOptions;
    }
    
    // 否则只显示匹配的 level_type
    return allLevelOptions.filter(option => option.label === level_type);
  }, [allLevelOptions, rootTemplateNode]);

  useEffect(() => {
    runGetClassifyLevel({key: 'dd:data_dams_classify_level'})
  }, [runGetClassifyLevel])
  
  const onSubmit = () => {
    templateForm.validateFields().then((values) => {
      // Cascader返回的是[parentValue, childValue]数组，我们需要childValue作为level_id
      const levelId = Array.isArray(values.templateName)
        ? values.templateName[values.templateName.length - 1]
        : values.templateName;

      // 使用新的API参数格式
      runBindClsLevel({
        id: Number(selectedTemplateNodeInfo?.id || 0), // integer类型
        level_id: Number(levelId) // integer类型
      })
    })
  };

  return (
    <UIModal
      title={t('classGrading.tab.tag.gradeConfig')}
      visible={true}
      onOk={onSubmit}
      onCancel={onCancel}
      confirmLoading={loading}
      width={600}
    >
      <Form form={templateForm}>
        <Form.Item
          label={t('classGrading.tab.template.level.label')}
          name="templateName"
        >
          <Cascader 
            options={levelOptions} 
            placeholder={t('classGrading.tab.template.level.plac')}
            showSearch
            changeOnSelect={true}
            expandTrigger="hover"
            popupClassName="grade-cascader-popup"
          />
        </Form.Item>
      </Form>
    </UIModal>
  );
}

export default GradeConfigModal;