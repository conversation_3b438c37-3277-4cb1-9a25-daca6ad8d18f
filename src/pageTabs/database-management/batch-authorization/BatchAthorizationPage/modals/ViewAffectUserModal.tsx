/**
 * 权限详情 modal
 */
import React, { useEffect, useState } from 'react'
import * as _ from 'lodash'
import { v4 as uuid } from 'uuid'
import { But<PERSON>, Tooltip, Modal, Popconfirm, Table, message, Typography } from 'antd'
import { DataSourceType, getBathAuthAffectUsers, IBatchAuthEffectUser, getBathAuthDeleteUser } from 'src/api'
import { useRequest } from 'src/hook'
import { Iconfont, PermissionTooltip } from 'src/components';
import styles from './index.module.scss';
import i18n from 'src/i18n'

interface IProp {
  nodePathWithType: string;
  resourceName: string;
  dataSourceType: DataSourceType;
  menuPermissionObj: any;
  handleClose: () => void;
}

const ViewAffectUserModal = (props: IProp) => {

  const defaultTablePagination = {
    current: 1,
    pageSize: 10
  }

  const { nodePathWithType, handleClose, resourceName, dataSourceType, menuPermissionObj } = props

  const [tablePagination, setTablePagination] = useState(defaultTablePagination)
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [selectedRows, setSelectedRows] = useState<IBatchAuthEffectUser[]>([]);
  // 获取影响用户
  const { data: effectUsers, loading, run: runGetBathAuthAffectUsers, refresh } = useRequest(getBathAuthAffectUsers,
    {
      formatResult(res) {
        return res?.map(item => ({...item, uuid:  uuid()}))
      },
      manual: true
    }
  )

  useEffect(() => {
    if (nodePathWithType) {
      runGetBathAuthAffectUsers({ nodePath: nodePathWithType })
    }
  }, [nodePathWithType])
  //删除
  const { run: runGetBathAuthDeleteUser, loading: batchClearLoading } = useRequest(getBathAuthDeleteUser, {
    manual: true,
    onSuccess() {
      message.success(i18n.t("batchAthorization:deleteSuccess"));
      refresh();
      setSelectedRows([]);
      setSelectedRowKeys([]);
      setTablePagination({...tablePagination, current: 1})
    }
  })

  const onBatchDelectAffectUser = () => {
    Modal.confirm({
      title: i18n.t("batchAthorization:confirmBatchDelete"),
      onOk: () => runGetBathAuthDeleteUser({
        deleteMap: selectedRows.reduce((acc: any, cur) => {
          acc[cur.roleId] = cur.permissionId;
          return acc;
        }, {}),
        userIds: selectedRows.map(item => item.userId),
        nodePath: nodePathWithType

      }),
      centered: true,
      okButtonProps: { loading: batchClearLoading },
    })
  }

  const columns = [
    {
      title: i18n.t("batchAthorization:user"),
      dataIndex: 'userName',
      key: 'userName',
      width: 100,
      fixed: 'left',
      render: (val: string, record: any) => {
        const name = `${val}(${record?.userId})`;
        return <Tooltip title={name}>
          <span>
            {name?.replace(/^(.{16}).+$/, (match: any, p1: string) => p1 + '...')}
          </span>
        </Tooltip>
      }
    },
    {
      title: i18n.t("batchAthorization:permissionLevel"),
      dataIndex: 'permissionName',
      key: 'permissionName',
      width: 100,
    },
    {
      title: i18n.t("batchAthorization:effectiveTime"),
      dataIndex: 'expr',
      key: 'expr',
      width: 150,
      render: (val: string) => {
        
        return (
          <Typography.Text  ellipsis>
            {val}
          </Typography.Text>
        )
      }
    },
    {
      title: i18n.t("batchAthorization:permissionSource"),
      dataIndex: 'source', 
      key: 'source',
      width: 120,
      filters: [
        {
          text: i18n.t('db.auth.modal.title'),
          value: 'USER',
        },
        {
          text: i18n.t('db.auth.toolPerm.autoPrivilege'),
          value: 'AUTO_USER',
        },
        {
          text: i18n.t('db.auth.toolPerm.flowPrivilege'),
          value: 'FLOW',
        }
      ],
      onFilter: (value: any, record: any) => record.sourceType === value,
    },
    {
      title: i18n.t("batchAthorization:authorizer"),
      dataIndex: 'authorizer',
      key: 'authorizer',
      width: 100,
    },
    {
      title: i18n.t("batchAthorization:authorizationTime"),
      dataIndex: 'authorizationTime',
      key: 'authorizationTime',
      width: 200,
    },
    {
      title: i18n.t("batchAthorization:operation"),
      dataIndex: 'canDelete',
      key: 'canDelete',
      width: 100,
      fixed: 'right',
      render: (txt: boolean, record: any) => (
        !record?.supportDelete ?
          <Tooltip title={record?.cannotDeleteMessage}>
            <Button
              type='link'
              disabled={true}
              style={{padding: '4px 0'}}
            >
              {i18n.t("batchAthorization:delete")}
            </Button>
          </Tooltip>
        :
          <Popconfirm
            title={i18n.t("batchAthorization:confirmDelete")}
            onConfirm={() => runGetBathAuthDeleteUser({
              deleteMap: { [record.roleId]: record.permissionId },
              userIds: [record.userId],
              nodePath: nodePathWithType,
            })}
            okText={i18n.t("batchAthorization:ok")}
            cancelText={i18n.t("batchAthorization:cancel")}
            disabled={!record?.supportDelete}
          >
            <Button type='link' style={{padding: '4px 0'}}>
              {i18n.t("batchAthorization:delete")}
            </Button>
          </Popconfirm>
      ),
    },
  ]
 
  const rowSelection: any = {
    selectedRowKeys: selectedRowKeys,
    onSelectAll(selected: boolean, newSelectedRows: any[]) {
      const curRowKeys = newSelectedRows.map(row => row?.uuid);

      setSelectedRowKeys(curRowKeys.filter(i => !_.isEmpty(i)));
      setSelectedRows(newSelectedRows.filter(i => !_.isEmpty(i)));
    },
    onSelect(item: any, selected: boolean) {

      let cloneSelectedRowKeys = _.cloneDeep(selectedRowKeys);
      let cloneSelectedRows = _.cloneDeep(selectedRows);
      if (selected) {
        cloneSelectedRows = cloneSelectedRows.concat([item])
        cloneSelectedRowKeys = cloneSelectedRowKeys.concat([item.uuid])
      } else {
        cloneSelectedRowKeys = cloneSelectedRowKeys.filter(k => k !== item.uuid)
        cloneSelectedRowKeys = cloneSelectedRowKeys.filter(k => k !== item.uuid)
      }
      setSelectedRows(cloneSelectedRows);
      setSelectedRowKeys(cloneSelectedRowKeys);
    },
    getCheckboxProps: (record: any) => ({
      disabled: !record?.supportDelete,
    }),
  };

  return (
    <Modal
      title={i18n.t("batchAthorization:affectedUsers")}
      key="permissionDetaiModal"
      visible={true}
      onCancel={handleClose}
      footer={null}
      width={900}
    >
      <div className='flexAlignCenterBetween mb10'>
        <div>
          {i18n.t("batchAthorization:resourceName")}：  <Iconfont type={`icon-connection-${dataSourceType}`} /> {resourceName}
        </div>
        <PermissionTooltip
          permissionlist={{ roleNameList: menuPermissionObj?.roleNameList || [], isOnlyRead: menuPermissionObj?.isOnlyRead }}
          width={200}
          title={i18n.t("batchAthorization:batchAuthorization")}
        >
          <Button type='primary' disabled={!selectedRowKeys?.length} onClick={() => onBatchDelectAffectUser()}>{i18n.t("batchAthorization:batchDelete")}</Button>
        </PermissionTooltip>
      </div>
      <Table
        rowKey='uuid'
        className={styles.scrollbarThumb}
        //@ts-ignore
        columns={columns}
        loading={loading}
        dataSource={effectUsers || []}
        rowSelection={rowSelection}
        pagination={{
          current: tablePagination?.current,
          pageSize: tablePagination?.pageSize,
          total: effectUsers?.length || 0,
          showTotal: (total: number) => `${i18n.t("batchAthorization:total")} ${total || 0} ${i18n.t("batchAthorization:items")}`,
          onChange: (pageNumber: number, pageSize: number | undefined) => {
            setTablePagination({ current: pageNumber ?? 1, pageSize: pageSize ?? 10 });
          }
        }}

        style={{ height: 400 }}
        scroll={{ x: 'max-content', y: 280 }}
      />
    </Modal>
  )
}

export default ViewAffectUserModal