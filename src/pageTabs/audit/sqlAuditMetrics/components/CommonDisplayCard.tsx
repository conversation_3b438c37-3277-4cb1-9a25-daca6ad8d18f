import React, { useContext, useEffect, useMemo, useState } from "react"
import styles from './component.module.scss'
import { AuditOverviewContext } from "../../overview/AuditOverviewContext";
import { getChart } from "../../customAuditMetrics/components/ChartCard";
import AutoDisplayTable from "../../overview/components/AutoDisplayTable";
import { useRequest, useSelector } from "src/hook";
import { findSqlMetricsData } from "src/api";
import { CUSTOM_AUDIT_CHART_ELEMENT } from "../../customAuditMetrics/constants";
import { checkChartCondition, checkChartGeneration, createChartData } from "../unit";
import { isEmpty } from "lodash";
import { Empty } from "antd";
import { useTranslation } from "react-i18next";

// 审计概览中sql模式审计指标的展示组件
export const CommonDisplayCard = (props: any) => {
  const { item } = props
  const { locales } = useSelector(state => state.login)
  const { t } = useTranslation()
  const { id, other = {} } = item
  const { chartsCtrlList } = useContext(AuditOverviewContext);
  const displayType = useMemo(() => {
    return chartsCtrlList.find((i: any) => i.id === item.id)?.displayType || 'TABLE'
  }, [chartsCtrlList, item])
  const [tableColumns, setTableColumns] = useState<any[]>([]) // 表格表头
  const [tableDataSource, setTableDataSource] = useState<any[]>([]) // 表格数据源
  const [chartData, setChartData] = useState<any>() // 图表的数据
  const [emptyDesc, setEmptyDesc] = useState<string>('') // 图表空状态的描述

  // 根据保存的指标id获取sql查询的数据
  const { run: getDataById, loading } = useRequest(findSqlMetricsData, {
    manual: true,
    onSuccess: (res) => {
      const { columnInfos, values } = res
      const resColumns = columnInfos?.map((item: any) => ({
        title: item?.columnName,
        dataIndex: item?.columnName,
        key: item?.columnName,
        width: 130,
        ellispe: true
      }))
      const resDataSource = values?.map((record: any, index: number) => ({
        ...record,
        key: String(index + 1),
      }))
      setTableColumns(resColumns || [])
      setTableDataSource(resDataSource || [])
    }
  })

  // 生成图表数据
  useEffect(() => {
    const { type = '', elementList = [], dataFormat = {} } = other
    if (type && elementList.length > 0) {
      const chartBaseInfo = CUSTOM_AUDIT_CHART_ELEMENT[type]
      const canCreate = checkChartCondition(chartBaseInfo?.dealType, elementList)
      if (canCreate) {
        if (tableColumns.length > 0 && tableDataSource.length > 0) {
          const data = createChartData(chartBaseInfo?.dealType, elementList, tableColumns, tableDataSource, dataFormat)
          setChartData(data)
        }
        else {
          setEmptyDesc(t("auays:emp_desc.no_metric_data"))
        }
      }
      else {
        setEmptyDesc(t("auays:emp_desc.invalid_bound_data"))
      }
    }
    else {
      setEmptyDesc(t("auays:emp_desc.no_bound_smart_chart"))
    }
  }, [other, tableColumns, tableDataSource, locales])


  useEffect(() => {
    if (id) {
      getDataById({ id })
    }
  }, [id])

  // 图表节点
  const chartNode = (type: string, data: any) => {
    // 判断是否图表能正常展示
    const canGenerate = checkChartGeneration(type, data)
    return <>
      {
        chartData && !isEmpty(chartData) ?
          typeof canGenerate === 'string' ?
            <div className={styles.noGeneraTipDiv}>
              <span>{canGenerate}</span>
            </div> :
            getChart(type, data, false, item.id, '450px') :
          <div className={styles.emptyNode}>
            <Empty description={emptyDesc} />
          </div>
      }
    </>
  }


  return <div className={styles.commonDisplayCard} key={item.id}>
    {
      displayType === 'CHART' ?
        chartNode(other?.type, chartData) :
        <AutoDisplayTable
          className={styles.displayTable}
          columns={tableColumns}
          dataSource={tableDataSource}
          pagination={false}
          loading={loading}
          scroll={{ x: 'fit-content', y: 'calc(45vh - 240px)' }}
        />
    }
  </div>
}