import { Modal, Form } from "antd";
import React, { useEffect } from "react";
import { useTranslation } from "react-i18next";
import styles from './index.module.scss'
import moment from "moment";
import { IUpdateDataParams } from "src/api";
import { useSelector } from "src/hook";
import { FormItemEffectiveTime } from 'src/pageTabs/flowPages/flowFormItems'

interface IEffectiveTimeModal {
  visible: boolean
  onCancel: () => void
  onOk: (params: IUpdateDataParams) => void
  defaultValue: any
  defaultParams?: any
  confirmLoading: boolean
}

const EffectiveTimeModal = (props: IEffectiveTimeModal) => {
  const { visible, onCancel, defaultValue: dfValues, onOk, defaultParams = {}, confirmLoading } = props
  const isLangEn = useSelector(state => state.login.locales) === 'en'
  const { t } = useTranslation()
  const [form] = Form.useForm();
  const customAuthValidOptions = useSelector(state => state.login.customAuthorizationPeriod)
  const hasForeverValidSettinng = customAuthValidOptions?.find(i => i?.timeValueUnit === 'forever')

  useEffect(() => {

     if (dfValues && form && !confirmLoading) {
     
       form.setFieldsValue({
           effectiveTime:   dfValues?.timeType === 1 ?  hasForeverValidSettinng ? "forever" : t('systemManagement.basic.validPeriod.forever') : "custom",
           rangeTime: dfValues?.rangeTime && dfValues?.rangeTime?.length > 0 ? [moment(dfValues.rangeTime[0]), moment(dfValues.rangeTime[1])] : undefined
         })
     }
  }, [dfValues, form, confirmLoading, hasForeverValidSettinng])

  const onSubmit  = () => {
    form.validateFields().then(values => {
   
      let params = {
        ...defaultParams,
        effectTimeType: ['forever', t('systemManagement.basic.validPeriod.forever')].includes(values?.effectiveTime) ? 'FOREVER' : 'CUSTOM',
      }

      if (values?.effectiveTime !== 'forever') {
        const beginTime = moment(values?.rangeTime?.[0]).valueOf();
        const endTime = moment(values?.rangeTime?.[1]).valueOf();
  
        params = {
          ...params,
          beginTime,
          endTime,
        }
      }
      onOk(params)
    })
  }

  return <Modal
    visible={visible}
    closable={false}
    onCancel={onCancel}
    confirmLoading={confirmLoading}
    onOk={onSubmit}
    width={450}
    destroyOnClose={true}
    className={styles.effectiveTimeModal}
    bodyStyle={{ padding: 16 }}
    maskClosable={false}
    centered={true}
  >
    <Form labelCol={{ span: isLangEn ? 8 : 4 }} form={form}>
      <FormItemEffectiveTime
        form={form}
        effectiveTimeRequired={false}
        defabultEffectiveTime={dfValues?.timeType === 1 ? "forever" : "custom"}
        effectiveTimeKey="effectiveTime"
        timeKey= "rangeTime"
      />
    </Form>
  </Modal>
}

export default EffectiveTimeModal