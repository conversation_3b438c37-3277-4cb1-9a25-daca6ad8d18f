/**
 * 权限详情 modal
 */
import { Button, Empty, Modal, Popconfirm, Table, Tabs, message, Form, Select, DatePicker, Dropdown, Menu, Input, Tooltip } from 'antd'
import { PlusOutlined, DownOutlined, EditOutlined } from '@ant-design/icons';
import React, { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { 
  delPermissionInfo, 
  getPermissionInfos,
  getObjectToolTemplatePost, 
  addPermission, 
  updatePermissionTimeNew,
  getPermissionTemplate,
  hasPermissionAddUser,
} from 'src/api'
import { useRequest } from 'src/hook'
import styles from './index.module.scss'
import classNames from "classnames";
import ToolPermissionSelect from './modals/ToolPermissionModal/ToolPermissionSelect'
import { getScrollX } from "src/util";
import EffectiveTimeModal from './EffectiveTimeModal';
import { cloneDeep } from 'lodash';
import { FormItemEffectiveTime } from 'src/pageTabs/flowPages/flowFormItems'

const { RangePicker } = DatePicker;

interface IProp {
  visible: boolean,
  setVisible: (v: boolean) => void
  params: any
  userId: string | undefined
  [p: string]: any
}
type IPermissionKeyMap = 
 'permissionOperation'
|'permissionTool'

const PermissionInfoModal = (props: IProp) => {

  const [form] = Form.useForm();
  const { t } = useTranslation()
  const { 
    visible, 
    setVisible, 
    params, 
    userId, 
    permissionlist, 
    activeTabKey,
    nodeType,
    nodePathWithType,
    dataSourceType,
    connectionId,
    groupId,
    selectNodeType,
    formTableLevel,  // 来源于表层级
    authorizeEnum
   } = props
  
  const [activeKey, setActiveKey] = useState<IPermissionKeyMap>('permissionOperation')
  const [permissionInfos, setPermissionInfos] = useState<any[]>([])
  const {isOnlyRead} = permissionlist || {}

  // 添加权限-操作权限可选项[{label,value}]
  const [allTemplateOperations, setAllTemplateOperations] = useState<any>([]);
  const [addMoreAdditionalTPM, setAddMoreAdditionalTPM] = useState(false);

  const [selectedRowKeys, setSelectedRowKeys] = useState<(number | string)[]>([]);
  const [selectedPermissionIds, setSelectedPermissionIds] = useState<(number | string)[]>([]); // 权限id可能相同，不能作为selectRowKey
  const [selectedOpMap, setSelectedOpMap] = useState<Record<number, string[]>>({});

  //编辑有效时间
  const [selectedToolPermissions, setSelectedToolPermissions] = useState<any>()  // 生效时间默认参数

  // 是否允许添加权限
  const [hasAddPermission, setHasAddPermission] = useState<boolean>(false); 
  const { run: queryHasAddPermission } = useRequest(hasPermissionAddUser, { manual: true });
  useEffect(() => {
    if (nodePathWithType || groupId) {
      const params = {
        groupId,
        nodePathWithType,
      };
      queryHasAddPermission(params)
        .then((result: any) => {
          setHasAddPermission(result);
        })
        .catch();
    }
  }, [nodePathWithType, groupId, queryHasAddPermission]);
  
  // 工具权限-可选权限列表
  const { data: toolPTemplates, run: runGetToolTemplateInfoPost } = useRequest(getObjectToolTemplatePost, {
    manual: true,
    onSuccess: (res) => {
      handlePermissionTemplateData(res)
    },
  })

  // 操作权限-可选权限列表
  const { data: permissionTemplates, run: runGetPermissionTemplate } = useRequest(getPermissionTemplate, {
    manual: true,
    onSuccess: (res) => {
      handlePermissionTemplateData(res)
    },
  })

  const handlePermissionTemplateData = (res: any) => {
    const formData: any = [];
    res?.forEach((item: any) => {
      item?.operations?.forEach((operation: any) => {
        formData.push({ value: operation?.operation, label: operation?.operationName, objectType: item?.objectType })
      })
    })
    setAllTemplateOperations(formData);
  }

  useEffect(() => {
    if (nodeType && dataSourceType && addMoreAdditionalTPM && activeKey === 'permissionTool') {
      // @ts-ignore
      runGetToolTemplateInfoPost(dataSourceType, nodeType, [nodePathWithType])
    }
    if (dataSourceType && addMoreAdditionalTPM && activeKey === 'permissionOperation') {
      runGetPermissionTemplate(dataSourceType)
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [nodeType, dataSourceType, addMoreAdditionalTPM, activeKey])

  // 添加工具权限
  const { run: runAddPermission, loading: addLoading } = useRequest(addPermission,
    {
      manual: true,
      onSuccess: (res) => {
        message.success(t('common.message.new_success'))
        onCancelAddAction()
        refreshPermissionInfos();
      },
    }
  );

  const onCancelAddAction = () => {
    setAddMoreAdditionalTPM(false);
    form?.resetFields();
  }

  // 工具权限生效时间
  const { run: runUpdateToolDate, loading: updateToolDateLoading } = useRequest(updatePermissionTimeNew, {
    manual: true,
    onSuccess() {
      setSelectedToolPermissions(null)
      message.success(t('common.message.edit.success'));
      setSelectedRowKeys([]);
      setSelectedOpMap({});
      refreshPermissionInfos();
    }
  })

  useEffect(() => {
    setActiveKey(activeTabKey)
  }, [activeTabKey])

  // 查询当前层级下的所有权限
  const {data: originPermissionInfos, run: getPermissionInfosRun, refresh: refreshPermissionInfos } = useRequest(
    getPermissionInfos,
    {
      manual: true,
      onSuccess: (res) => {
        const result = Array.isArray(res) ? [...res] : [];
        setPermissionInfos(cloneDeep(result)) // 需要深拷贝避免影响原始数据
      },
      onError: () => {
        setPermissionInfos([])
      }
    }
  )

  useEffect(() => {
    getPermissionInfosRun(params)
  }, [getPermissionInfosRun, params])

  // 删除权限
  const { run: delPermissionInfoRun } = useRequest(
    delPermissionInfo,
    {
      manual: true,
      onSuccess: () => {
        message.success(t('common.message.delete_success'))
        getPermissionInfosRun(params)
      },
      onError: () => {
        message.error(t('common.massage.delete.failed'))
      }
    }
  )

  const handleCancel = () => {
    setVisible(false)
  }

  const onConfirm = (item: any) => {
    const delParams = {
      userId: userId,
      permissionIds: [item.permissionId],
      nodePathWithType: params.nodePathWithType,
      operationMap: {[item.permissionId]: [item.operation]},
    }
    delPermissionInfoRun(delParams)
  }

  const columns: any = [
    {
      title: t('db.auth.connectSetting.tab.users.permissionName'),
      dataIndex: 'permissionName',
      key: 'permissionName',
      width: 120,
    },
    {
      title: t('db.auth.permDetail.sourceType'),
      dataIndex: 'sourceType',
      key: 'sourceType',
      width: 120,
      filters: [
        {
          text: t('db.auth.modal.title'),
          value: 'USER',
        },
        {
          text: t('db.auth.toolPerm.autoPrivilege'),
          value: 'AUTO_USER',
        },
        {
          text: t('db.auth.toolPerm.flowPrivilege'),
          value: 'FLOW',
        }
      ],
      onFilter: (value: any, record: any) => record.sourceType === value,
      render: (_: string, record: any) => {
        return (
          <span>{record?.source}</span>
        )
      }
    },
    {
      title: t('db.auth.connectSetting.tab.users.resource'),
      dataIndex: 'path',
      key: 'path',
      width: 100,
    },
    {
      title: t('db.auth.connectSetting.tab.users.effectiveTime'),
      dataIndex: 'expr',
      key: 'expr',
      width: 180,
      render: (val: string, record: any) => {
        return (
          <div className={styles.effectTimeRender}>
            <div className={styles.timeContent}>{val}</div>
            {
              !record?.canEdit
                ?
                  <Tooltip title={record?.cannotEditMessage}> 
                    <EditOutlined style={{cursor: 'not-allowed'}} />
                  </Tooltip>
                :
                  <EditOutlined 
                    onClick={() => {
                      const timeType = val !== t('db.conn.effectTive.forever') ? 2 : 1
                      let rangeTime: any[] = []
                      if (val !== t('db.conn.effectTive.forever')) {
                        const stringTimes = val.split(' - ')
                        const beginDate = Date.parse(stringTimes?.[0])
                        const endDate = Date.parse(stringTimes?.[1])
                        rangeTime = endDate && beginDate ? [beginDate, endDate] : []
                      }
                      setSelectedToolPermissions({
                        userId: record?.authorizerId,
                        nodePathWithType: record?.nodePathWithType || undefined, // 组级可能没有
                        uniqueFields: selectNodeType === 'group' ? [record?.operation]: [record?.permissionId],
                        timeType,
                        rangeTime,
                        operation: record?.operation,
                        permissionId: record?.permissionId
                      })
                    }} 
                  />
            }
          </div>
        )
      }
    },
    {
      title: t('db.auth.connectSetting.tab.users.authorizer'),
      dataIndex: 'authorizer',
      key: 'authorizer',
      width: 120,
    },
    {
      title: t('db.auth.connectSetting.tab.users.authorizationTime'),
      dataIndex: 'authorizationTime',
      key: 'authorizationTime',
      width: 160
    },
    {
      title: t('common.text.action'),
      dataIndex: 'canDelete',
      key: 'canDelete',
      fixed: 'right',
      width: 80,
      render: (txt: boolean, record: any) => (
        // <PermissionTooltip
        //   title={t('db.auth.modal.title')}
        //   permissionlist={permissionlist}
        // >
          <Popconfirm
            title={t('db.auth.permDetail.delete.tip')}
            onConfirm={() => onConfirm(record)}
            okText={t('common.btn.confirm')}
            cancelText={t('common.btn.cancel')}
            disabled={!record?.canDelete}
          >
            {
              record?.canDelete
                ?
                  <Button
                    type='link'
                    danger
                  >
                    {t('common.btn.delete')}
                  </Button>
                :
                  <Tooltip title={record?.cannotDeleteMessage}> 
                    <Button
                      type='link'
                      danger
                      disabled={true}
                    >
                      {t('common.btn.delete')}
                    </Button>
                  </Tooltip>
            }
          </Popconfirm>
        // </PermissionTooltip>
      ),
    },
  ]

  const rowSelection = {
    selectedRowKeys: selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: any[]) => {
      const selectedPermissionIds = selectedRows.map((row: any) => row.permissionId);
      setSelectedRowKeys(selectedRowKeys)
      setSelectedPermissionIds(selectedPermissionIds)
      // 创建普通对象存储 id → operation[] 映射
      const newSelectedOps: Record<number, string[]> = {};
      selectedRows.forEach(row => {
        // 如果 permissionId 不存在，初始化一个空数组
        if (!newSelectedOps[row.permissionId]) {
          newSelectedOps[row.permissionId] = [];
        }
        // 将 operation 添加到数组中（避免重复可以加检查）
        newSelectedOps[row.permissionId].push(row.operation);
      });
      setSelectedOpMap(newSelectedOps);
    },
    getCheckboxProps: (record: any) => ({
      disabled: !record?.canEdit && !record?.canDelete,
    }),
  }

  const renderTabPane = (tabData: any) => {
    const { tabName, tabKey, tabContent } = tabData
    return (
      <Tabs.TabPane tab={tabName} key={tabKey}>
        <Table
          dataSource={tabContent}
          columns={columns}
          pagination={false}
          scroll={{x: getScrollX(columns), y: 'calc(80vh - 280px)' }}
          rowSelection={rowSelection}
          rowKey={(record: any) => record?.operationKey}
        />
      </Tabs.TabPane>
    )
  }

  const handleTabsChange = (key: string) => {
    // 校验是否是合法的 IPermissionKeyMap 值
    if (key === 'permissionOperation' || key === 'permissionTool') {
      setActiveKey(key)
      form.setFieldsValue({ operations: [] })
    }
  }

  // 添加权限
  const onAddMoreATPM = () => {
    form.validateFields(['operations', 'effectiveTime', 'rangeTime']).then(values => {
    
      const { effectiveTime, rangeTime = undefined } = values;
      if (!effectiveTime || (effectiveTime !== 'forever' && !rangeTime)) {
        return message.warning(t('db.auth.connectSetting.tab.users.timeRange.plac'))
      }
  
      let addParams: any = {
        operations: values?.operations,
        dataSourceType,
        nodeType,
        userIds: [userId],
        permissionType: activeKey === 'permissionTool' ? 'permissionTool' : 'dataSource',  // 操作权限(dataSource)/工具权限(permissionTool)
        effectTimeType: effectiveTime === 'forever' ? 'FOREVET' : 'CUSTOM',
      }
      if (nodeType === 'group') {
        addParams.groupId = groupId;
      } else {
        addParams.connectionId = Number(connectionId);
        addParams.nodePathWithTypes = [nodePathWithType];
      }
      if (effectiveTime !== 'forever' && rangeTime) {
        addParams.beginTime = rangeTime[0];
        addParams.endTime = rangeTime[1];
      }

      runAddPermission({ ...addParams })
    })
  }

  //批量删除工具权限
  const { run: runRevokePermission, loading: revokePermissionLoading } = useRequest(delPermissionInfo, {
    manual: true,
    onSuccess() {
      message.success(t('common.message.delete_success'));
      setSelectedRowKeys([]);
      setSelectedOpMap({});
      refreshPermissionInfos();
    }
  })

   const handleDelete = (ids: (number | string)[], opMap: Record<number, string[]>) => {
    Modal.confirm({
      centered: true,
      title: t('db.auth.connectSetting.tab.users.deleteSelectedPerm'),
      okButtonProps: { loading: revokePermissionLoading },
      onOk: () => {
        // @ts-ignore
        runRevokePermission({
          ...(nodeType === 'group' && groupId ?
            {
              groupId: Number(groupId),
              operations: ids
            } : {
              permissionIds: ids,
            }),
          nodePathWithType,
          userId: userId || '',
          operationMap: opMap,
        })
      }
    })
  };

  const menu = (
    <Menu>
      <Menu.Item onClick={() => { handleDelete(selectedPermissionIds, selectedOpMap) }}>
        {t('common.btn.batchDelete')}
      </Menu.Item>
      <Menu.Item onClick={() => { setSelectedToolPermissions({ uniqueFields: selectedPermissionIds }); }}>
        {t('db.auth.connectSetting.tab.users.batchEffectTime')}
      </Menu.Item>
    </Menu>
  )

  // 前端搜索(不区分大小写)
  const handleSearch = (value: string) => {
    if([null, undefined, ''].includes(value)) {
      const originPermissionInfosCopy = cloneDeep(originPermissionInfos)
      return setPermissionInfos(originPermissionInfosCopy)
    }
    setPermissionInfos((d: any[])=>{
      const searchVal = value?.toLowerCase();
      const originPermissionInfosCopy = cloneDeep(originPermissionInfos)
      return originPermissionInfosCopy.filter((i: any) => {
        i.tabContent = i?.tabContent?.filter((j: any) => {
          const lowerPermissionName = j?.permissionName?.toLowerCase();
          const lowerPath = j?.path?.toLowerCase();
          const lowerAuthorizer = j?.authorizer?.toLowerCase();
          return lowerPermissionName?.includes(searchVal) || lowerPath?.includes(searchVal) || lowerAuthorizer?.includes(searchVal)
        })
        return {...i}
      })
    })
  }

  // 操作权限渲染
  const renderAuthorizeList = useCallback(() => { 
    if (authorizeEnum) {
      const authOptions = authorizeEnum?.filter((item: any) => !item?.isToolPermission);
      const options = authOptions?.filter((item: any) => item.canSelect)?.map((item: any) => {
        return {
          label: item?.operationName,
          value: item?.operation,
        };
      });
      return (
        <Select
          placeholder={t('common.search.select.placeholder')}
          mode="tags"
          maxTagCount={4}
          options={options}
          style={{ width: 140 }}
          onChange={(values: number[]) => {
            form.setFieldsValue({ operations: values });
          }}
        />
      );
    }

  }, [authorizeEnum, form, t])

  // table层级需要带上用户信息
  return (
    <Modal
      title={t('db.auth.connectSetting.tab.users.permissionInfo')}
      key="permissionDetaiModal"
      visible={visible}
      onCancel={handleCancel}
      footer={null}
      width={1240}
    >
      <div className={classNames('mb10 flexAlignCenterBetween')}>
        {/* 支持检索权限名称，资源，授权人 */}
        {
          !addMoreAdditionalTPM && (
            <Input.Search 
              style={{width: 350}}
              placeholder={t('db.conn.autoAuth.search.placeholder')}
              onSearch={handleSearch}
              allowClear
            />
          )
        }
        <div className="flex">
          <div className="flexAlignCenter">
            {
              addMoreAdditionalTPM ? (
                <Form form={form} layout='inline' className={styles.addHeader}>
                  <Form.Item
                    required
                    label={
                      activeKey === 'permissionTool' ?
                        t('db.auth.connectSetting.tab.users.tool')
                        : t('db.auth.connectSetting.tab.users.fineGrit')
                      }
                    name='operations'
                    rules={[{ required: true, message: t('db.auth.connectSetting.tab.users.operations.tip') }]}
                    id='adptFormItem'
                  >
                    {
                      // 表层级的添加权限-操作权限特殊处理
                      (formTableLevel && activeKey === 'permissionOperation')
                      ?
                        renderAuthorizeList()
                      :
                        <ToolPermissionSelect
                          options={allTemplateOperations}
                          dropdownTemplates={ activeKey === 'permissionTool' ? toolPTemplates : permissionTemplates}
                          onChange={(values: number[]) => {
                            form.setFieldsValue({ operations: values });
                          }}
                          placeholder={
                            activeKey === 'permissionTool' ?
                            t('db.auth.connectSetting.tab.users.operations.tip')
                            : t('pleaseSelect')
                          }
                        />
                    }
                  </Form.Item>
                  <FormItemEffectiveTime
                    form={form}
                    effectiveTimeRequired={true}
                    effectiveTimeKey="effectiveTime"
                    timeKey="rangeTime"
                  />
                  <Form.Item>
                    <Button type="primary" loading={addLoading} onClick={() => onAddMoreATPM()}>{t('common.btn.confirm')}</Button>
                    <Button className="ml10" onClick={onCancelAddAction}>{t('common.btn.cancel')}</Button>
                  </Form.Item>
                </Form>
              ) : (
                <Button
                  type='link'
                  icon={<PlusOutlined color='#3262ff' />}
                  disabled={!hasAddPermission}
                  onClick={() => setAddMoreAdditionalTPM(true)}
                >
                  {t('db.auth.connectSetting.tab.users.addPerm')}
                </Button>
              )
            }
          </div>
          <Dropdown disabled={!selectedRowKeys?.length} overlay={menu}>
            <Button type="primary">{t('common.btn.batchText')}<DownOutlined /></Button>
          </Dropdown>
        </div>
      </div>
        
      {
        !permissionInfos || permissionInfos.length === 0 ?
          <Empty className={styles.empty} description={t('db.auth.permDetail.loading')} style={{ height: 400 }}></Empty>
          :
          <Tabs 
            activeKey={activeKey} 
            onChange={handleTabsChange}
          >
            {
              permissionInfos?.map((item: any) => {
                return renderTabPane(item)
              })
            }
          </Tabs>
      }

      {/* 生效时间 */}
      {
        selectedToolPermissions && 
        <EffectiveTimeModal
          confirmLoading={updateToolDateLoading}
          visible={true}
          onCancel={() => { setSelectedToolPermissions(null) }}
          onOk={(params: any) => {
            // delete selectedToolPermissions?.rangeTime;
            // delete selectedToolPermissions?.timeType;

            runUpdateToolDate({
              ...params,
              userId,
              nodePathWithType,
              ...(nodeType === 'group' ?
                {
                  groupId: Number(groupId),
                  operations: selectedToolPermissions?.uniqueFields
                }
                :
                {
                  permissionIds: selectedToolPermissions?.uniqueFields
                }),
              operationMap: selectedRowKeys?.length ? selectedOpMap : {[selectedToolPermissions.permissionId]: [selectedToolPermissions.operation]},
            })
          }
          }
          defaultValue={{ timeType: selectedToolPermissions?.timeType, rangeTime: selectedToolPermissions?.rangeTime }}
        />
      }
    </Modal>
  )
}

export default PermissionInfoModal