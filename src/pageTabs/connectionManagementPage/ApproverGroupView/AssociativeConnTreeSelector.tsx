import React, { useEffect, useRef, useState, useMemo, memo } from 'react'
import { TreeSelect } from 'antd'
import { useTranslation } from 'react-i18next'
import type { DataNode } from 'antd/es/tree'
import { LegacyDataNode } from 'rc-tree-select/lib/interface'
import { queryGroupNodes } from 'src/api'
import { formatConnTreeSelectValues } from '../ApproverGroupModal'
import { Iconfont } from 'src/components'
import { useRequest } from 'src/hook'

interface IProps {
  defaultSelectedConnKeys: any;
  onChange?: (value: LegacyDataNode[]) => void
}

const renderNodeItemKey = (nodeType:'datasource' |'connection' | 'group', id: number | string) => {
  switch (nodeType) {
    case 'datasource':
      return id;
    case 'group':
      return `${nodeType}_${id}`
    default:
      return Number(id);
  }
}
// 构造树结构  
export const generateTree = (data: any[], treeMapRef: any) => {
  const allId = data
    ?.map((i) => i.id)
    ?.filter((i, index, arr) => arr.indexOf(i) === index)
  const allParentId = data
    ?.map((i) => i.parentId)
    ?.filter((i, index, arr) => arr.indexOf(i) === index)

  const validateParentId = (item: any) => {
    return !!data?.filter((i: any) => {
      return i?.parentId === item?.id && item?.nodeType !== i?.nodeType
    })?.length
  }

  const filterData = data.filter((item, _, arr) => {
    // 自身是父级（且id和parentId不能重复）
    if (allParentId.includes(item.id) && validateParentId(item)) {
      item.children = arr.filter((i) => i.parentId === item.id)
    }
    // 没有父级
    if (!allId.includes(item.parentId) || !item.parentId) {
      return true
    }
    return false
  })

  const formatTree = (data: any[]): any[] =>
    data.map((item: any) => {
      const { nodeType, nodeName, id, connection = {} } = item;
      const isEmptyGroup = item.nodeType === 'group' && !item.children?.length;
      item.connectionType = item?.nodeType !== 'datasource' ? connection?.connectionType : nodeName;
      item.key = renderNodeItemKey(item?.nodeType, id) // 唯一key
      item.title = item.nodeName
      let icon = `icon-${nodeType}`;
      item.isLeaf = nodeType === "connection" || isEmptyGroup;
      item.disabled = isEmptyGroup;

      if (nodeType === "connection") {
        icon = `icon-${connection?.connectionType}`;
      } else if (nodeType === 'datasource') {
        icon = `icon-connection-${nodeName}`;
      } else if (nodeType === 'group') {
        icon = "icon-shujukuwenjianjia"
      }
      item.icon = <Iconfont type={icon} />
      if (item.children) {
        item.children = formatTree(item.children);
      }
      treeMapRef?.current?.set(item.key, item)
      return { ...item }
    })
  return formatTree(filterData)
}

const AssociativeConnTreeSelector = memo(({
  defaultSelectedConnKeys,
  onChange
}: IProps) => {
  const { SHOW_CHILD } = TreeSelect
  const { t } = useTranslation();
  // tree map, 记录元素树节点的引用
  const treeMapRef = useRef(new Map<string, LegacyDataNode>())
  const [treeData, setTreeData] = useState<DataNode[]>([])
  const [checkedNodeItems, setCheckedNodeItems] = useState<LegacyDataNode[]>([]);
  const treeDataRef = useRef<DataNode[]>([]) // 记录完整的树数据

  // 左侧treeData
  const { data: treeWrapData } = useRequest(() => queryGroupNodes(false), {
    formatResult(res) {
      return generateTree(res?.nodeList || [], treeMapRef).filter(node => node?.children?.length);
    },
  });


  useEffect(() => {
    setTreeData(treeWrapData ?? []);
    treeDataRef.current = treeWrapData ?? []
  }, [treeWrapData])

  useEffect(() => {
    if (defaultSelectedConnKeys?.length) {
      setCheckedNodeItems(formatConnTreeSelectValues(defaultSelectedConnKeys));
    }

  }, [defaultSelectedConnKeys])

  const onChangeValue = (checkedValues: any) => {

    const targetNodes: any[] = []
    checkedValues.forEach((i: any) => {
      const targetNode = treeMapRef.current.get(i.value)
      if (targetNode) {
        targetNodes.push(targetNode)
      }
    })
    setCheckedNodeItems(targetNodes)
    onChange?.(targetNodes);
  }

  const getIcon = (node: any) => {
    const { nodeType, connection = {} } = node || {};
    let icon = `icon-${nodeType}`;
    if (nodeType === "connection") {
      icon = `icon-${connection?.connectionType}`;
    } else if (node?.nodeType === 'datasource') {
      icon = `icon-connection-${node?.connectionType}`;
    } else if (nodeType === 'group') {
      icon = "icon-shujukuwenjianjia"
    }
    return icon
  }

  const labeledValue = useMemo(() => {
    if (checkedNodeItems) {
      return checkedNodeItems.map(item => ({
        ...item,
        value: item.key,
        label: <div style={{ display: 'flex', alignItems: 'center' }}>
          <Iconfont
            type={getIcon(item)}
            style={{ marginRight: 4 }} />
          {item.nodeName}
        </div>
      }))
    }
    return null
  }, [checkedNodeItems])
 
  return (
    <TreeSelect
      key='approverGroupTree'
      allowClear
      showArrow
      labelInValue
      value={labeledValue}
      treeData={treeData}
      onChange={onChangeValue}
      treeCheckable={true}
      showCheckedStrategy={SHOW_CHILD}
      placeholder={t('db.connection.approver.connections.plac')}
      treeIcon={true}
      maxTagCount={3}
      autoClearSearchValue={false}
      dropdownStyle={{ width: 'maxContent', maxHeight: "400px" }} // 设置下拉框宽度自适应于内容
      treeNodeFilterProp='title'
    />
  );
});

export default AssociativeConnTreeSelector;