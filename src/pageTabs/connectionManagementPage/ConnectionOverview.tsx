/**
 * 连接概览
 */
import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useHistory } from 'react-router-dom'
import { Card, Row, Col, Spin, Tooltip, Button } from 'antd'
import { useRequest, useDispatch } from 'src/hook'
import { setIsTabEditConnection } from 'src/pageTabs/connectionManagementPage/connectionManagementPageSlice'
import {
  queryDBTableCount,
  queryConnectionCount,
  querySQLCount,
} from 'src/api'
import { Iconfont } from 'src/components'
import TextWithTooltip from 'src/components/TextWithTooltip'
import classnames from 'classnames'
import styles from './index.module.scss'
import { parseFileNameFromPath } from 'src/util/connectionManage'
import { 
  setOverviewPageState,
  setOverviewPageDetailParams 
} from 'src/pageTabs/audit/overview/overviewSlice'

interface IProps {
  setCurTabs: (p: string) => void
  handleShowContentChange: (to: string, form?: string) => void
  [p: string]: any
}
const ConnectionOverview = (props: IProps) => {
  const history = useHistory();
  const {
    connectionId,
    connectionName,
    dataSourceType,
    connectionPool,
    connectionPoolLoading,
    setCurTabs,
    handleEditContent,
    permissionlist,
    canEdit,
    isArchiveConnection,
  } = props

  const { roleNameList } = permissionlist || {}

  const { t } = useTranslation()
  const dispatch = useDispatch()

  // 库表统计数据
  const {
    data: dbSchemaNums,
    loading: dbSchemaNumsLoading,
    run: getDBTableCount,
  } = useRequest(queryDBTableCount, { manual: true })

  // 查询数据源连接池连接数量
  const {
    data: connectionNums,
    loading: connectionNumsLoading,
    run: getConnectionCount,
  } = useRequest(queryConnectionCount, {
    manual: true,
  })

  // 查询今日SQL、总SQL
  const { data: sqlCountInfo, loading: sqlCountLoading, run: getSQLCount } =
    useRequest(querySQLCount, {
      manual: true
    })

  useEffect(() => {
    if (connectionName && connectionId) {
      getSQLCount(connectionName)
      getDBTableCount(connectionId)
      getConnectionCount(connectionId)
    }
  }, [
    connectionName,
    connectionId,
    getSQLCount,
    getDBTableCount,
    getConnectionCount,
  ])

  const hideAddressPortFlag = !!connectionPool?.filter((i: any)=>i?.field==="connectionMode" && i?.value===t('db.connection.tabs.overview.connectionMode.replicaSet'))?.length

  // 渲染语句明细
  const gotoStatementDetail = (params: any) => {
    history.push('/audit_view')
    dispatch(setOverviewPageState('statement_detail'))
    dispatch(setOverviewPageDetailParams(params))
  }

  return (
    <div className={styles.connectionOverviewTabs}>
      <Card hoverable className={classnames(styles.noCursor, styles.cardWrap)}>
        <Spin spinning={connectionPoolLoading}>
          <div className={styles.header}>
            <Iconfont
              type={`icon-${dataSourceType}`}
              className={classnames(
                styles.mr10,
                styles.fs30,
                styles.bgcolorebefff,
                styles.circle48
              )}
            />
            <div>
              <div className={styles.title}>{dataSourceType}</div>
              <div className={styles.desc}>{t('db.connection.tabs.overview.dataSourceType')}</div>
            </div>
          </div>
          {connectionPool?.map((item: any, index: string) => {
            const showValue = () => {
              if (item?.type === 'table') { //table类型展示
                return item?.value?.map((i: any) => 
                  <div style={{ marginBottom: 6 }} key={i?.connectionUrl + i?.connectionPort}>
                    <TextWithTooltip 
                      text={i?.connectionUrl + ':' + i?.connectionPort} 
                      maxLength={16} 
                    />
                  </div>)
              }
              else if (item.field === "isAdminWithAllPerms") { // 是否获得此连接的所有访问权限（布尔类型）
                return item.value ? t('common.btn.enable') : t('common.btn.forbidden')
              }
              else if(item.field ==='keytab' || item.field ==='krb5_config') {
                return item.value ? parseFileNameFromPath(item.value) : undefined
              }
              else if (item?.value?.length > 13) { 
                return (
                  <Tooltip title={item?.value}>
                    {item?.value?.substring(0, 13) + "..."}
                  </Tooltip>
                )
              }
              else {
                return item?.value
              }
            }

            // 隐藏密码、备注
            if(["remark", "password"].includes(item.field)){
              return null
            }
            // 连接模式是副本集模式时不展示地址和端口
            if(hideAddressPortFlag && ["connectionUrl", "connectionPort"].includes(item?.field)){
              return
            }
            return (
              <Row className={styles.mb10} key={index}>
                <Col span={12} style={{ color: '#868FA3' }}>{item?.label}：</Col>
                <Col span={12}>{showValue()}</Col>
              </Row>
            );
          })}
          {
            canEdit 
            ? <Button
              type='link'
              className={classnames(styles.options, styles.padding0)}
              onClick={() => {
                dispatch(setIsTabEditConnection(true))
                handleEditContent({ connectionId });
              }}
              style={{padding: 0}}
            >
              { isArchiveConnection ? t('common.btn.viewDetail') : t('db.connection.sdt.editConn') }
            </Button>
            : <Tooltip title={t('db.connection.noPerm', {roleNameList: roleNameList?.join(',') })}>
                <Button
                  type='link'
                  className={classnames(styles.options, styles.padding0)}
                  style={{padding: 0}}
                  disabled={true}
                >
                  {t('db.connection.sdt.editConn')}
                </Button>
              </Tooltip>  
          }
        </Spin>
      </Card>
      <div className={styles.cardWrap}>
        <Card
          hoverable
          onClick={() => { !isArchiveConnection && setCurTabs("3")}}
          style={isArchiveConnection? {backgroundColor: '#e5e5e5', cursor: 'not-allowed'} : {}}
        >
          <Spin spinning={connectionNumsLoading}>
            <div className={styles.flex}>
              <div className={styles.flex1}>
                <div className={styles.nums}>
                  <span className={styles.col3357ff}>
                    {connectionNums?.userNum || 0}
                  </span>
                  /
                  <span className={styles.col3357ff}>
                    {connectionNums?.connectionNum || 0}
                  </span>
                </div>
                <div>{t('db.connection.tabs.overview.userAndConnCount')}</div>
              </div>
              <Iconfont
                type="icon-renyuan"
                className={classnames(
                  styles.fs30,
                  styles.bgcolor3357FF,
                  styles.circle64,
                  styles.ml10
                )}
              />
            </div>
          </Spin>
        </Card>
        <Card
          hoverable
          className={styles.mt20}
          onClick={() => gotoStatementDetail({ connectionIds: [connectionId] }) }
        >
          <Spin spinning={sqlCountLoading}>
            <div className={styles.flex}>
              <div className={styles.flex1}>
                <div className={styles.nums}>
                  <span className={styles.col3357ff}>
                    {sqlCountInfo?.today || 0}
                  </span>
                  /
                  <span className={styles.col3357ff}>
                    {sqlCountInfo?.total || 0}
                  </span>
                </div>
                <div className={styles.desc}>{t('db.connection.tabs.overview.sqlAndExecuteCount')}</div>
              </div>
              <Iconfont
                type="icon-ai-code"
                className={classnames(
                  styles.fs30,
                  styles.bgcolor23B899,
                  styles.circle64,
                  styles.ml10
                )}
              />
            </div>
          </Spin>
        </Card>
      </div>
      <Card
        hoverable
        className={styles.cardWrap}
        onClick={() => { !isArchiveConnection && setCurTabs("2")}}
        style={isArchiveConnection? {backgroundColor: '#e5e5e5', cursor: 'not-allowed'} : {}}
      >
        <Spin spinning={dbSchemaNumsLoading}>
          <div className={classnames(styles.title, styles.mb20)}>{t('db.connection.tabs.overview.resourceManagement')}</div>
          <div className={styles.items}>
            <span>
              <Iconfont
                type="icon-schema_disable"
                className={classnames(
                  styles.fs18,
                  styles.color2B58BA,
                  styles.mr4
                )}
              />
              {t('db.connection.tabs.overview.logicalBase')}
            </span>
            <span className={classnames(styles.nums, styles.col3357ff)}>
              {dbSchemaNums?.databaseNum || 0}
            </span>
          </div>
          <div className={styles.items}>
            <span>
              <Iconfont
                type="icon-schema_nomal"
                className={classnames(
                  styles.fs18,
                  styles.colorgreen,
                  styles.mr4
                )}
              />
              Schema
            </span>
            <span className={classnames(styles.nums, styles.col3357ff)}>
              {dbSchemaNums?.schemaNum || 0}
            </span>
          </div>
          <div className={styles.items}>
            <span>
              <Iconfont
                type="icon-table"
                className={classnames(
                  styles.fs18,
                  styles.color2B58BA,
                  styles.mr4
                )}
              />
              {t('db.connection.tabs.overview.tableCount')}
            </span>
            <span className={classnames(styles.nums, styles.col3357ff)}>
              {dbSchemaNums?.tableNum || 0}
            </span>
          </div>
        </Spin>
      </Card>
    </div>
  );
}

export default ConnectionOverview
