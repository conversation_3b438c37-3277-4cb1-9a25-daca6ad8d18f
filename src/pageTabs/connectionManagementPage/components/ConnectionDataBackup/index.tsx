import React, { useEffect, useState, memo } from 'react';
import { QuestionCircleOutlined } from '@ant-design/icons';
import {
  Form,
  Switch,
  Tooltip,
  Space,
  Row,
  Col,
  Button,
  message
} from 'antd';
import * as _ from 'lodash';
import classnames from 'classnames'
import { useSelector } from 'src/hook';
import { useTranslation } from 'react-i18next';
import {
  updateConnectionWorkConfig,
  getConnectionConfig
} from 'src/api'
import { SqlBackupLocationTree } from './SqlBackupLocationTree';
import CertiEffectiveTime from 'src/pageTabs/settingPage/settingCards/CertiEffectiveTime'
import styles from '../../index.module.scss';

interface IProps {
  connectionId?: string;
  dataSourceType: string;
  [p: string]: any
}

export const ConnectionDataBackup = memo((props: IProps) => {
  const { connectionId, dataSourceType, permissionlist, canEdit, operationType, isArchiveConnection = false } = props;

  const [form] = Form.useForm();
  const { t } = useTranslation();
  const isLangEn = useSelector((state) => state.login.locales) === 'en'

  const thisForm = (operationType && operationType === "connectionConfig") ? (props?.form) : form
  //数据备份状态
  const [sqlBackupStatus, setSqlBackupStatus] = useState(false);
  //初始化数据
  const [initSqlBackupValues, setInitSqlBackupValues] = useState<any>({});

  const [backupIsEdit, setBackupIsEdit] = useState(false)

  const getSqlBackupSetting = async () => {

    await Promise.all([
      getConnectionConfig({
        connectionId,
        variable: 'sqlBackUp',
      }),
      getConnectionConfig({
        connectionId,
        variable: 'sqlBackUpLocation',
      }),
      getConnectionConfig({
        connectionId,
        variable: 'sqlFlashBack',
      }),
      getConnectionConfig({
        connectionId,
        variable: 'sqlBackUpTime',
      }),
    ]).then(([res1, res2, res3, res4]) => {

      const backupTime = JSON.parse(res4?.variable_value || '{}');
      const res = {
        sqlBackupLocation: res2?.variable_value,
        sqlBackupStatus: JSON.parse(res1?.variable_value) || false,
        sqlFlashBackSetting: JSON.parse(res3?.variable_value) || false,
        sqlBackUpTime: JSON.stringify({
          day: backupTime?.dayOfWeek ?? 8,
          startTime: backupTime?.startTime ?? "00:00",
          endTime: backupTime?.endTime ?? "23:59",
        })
      }

      thisForm.setFieldsValue(res);
      setInitSqlBackupValues({...res, connectionName:res2?.connectionName });
      setSqlBackupStatus(res?.sqlBackupStatus)
    });
  };
  useEffect(() => {
    if (connectionId) {
      getSqlBackupSetting()
    } else {
      thisForm.setFieldsValue({
        sqlBackUpTime: JSON.stringify({
          day: 8,
          startTime: "00:00",
          endTime: "23:59",
        })
      });
    }
  }, [connectionId])
  //切换备份状态设置默认值
  useEffect(() => {

    if (sqlBackupStatus && thisForm) {
      thisForm.setFieldsValue({
        sqlBackUpTime:
          connectionId ?
            initSqlBackupValues?.sqlBackUpTime
            : JSON.stringify({
              day: 8,
              startTime: "00:00",
              endTime: "23:59",
            })
      })
    }
  }, [sqlBackupStatus, thisForm])

  const onChangeBackupSetting = async (status: boolean, location: string, sqlFlashBackSetting: boolean, sqlBackUpTime: any) => {
    await Promise.all([
      updateConnectionWorkConfig({
        connectionId,
        variable: 'sqlBackUp',
        variable_value: status,
      }),
      status && updateConnectionWorkConfig({
        connectionId,
        variable: 'sqlBackUpLocation',
        variable_value: location,
      }),
      status && updateConnectionWorkConfig({
        connectionId,
        variable: 'sqlFlashBack',
        variable_value: sqlFlashBackSetting,
      }),
      status && connectionId && updateConnectionWorkConfig({
        connectionId,
        variable: 'sqlBackUpTime',
        variable_value: JSON.stringify({
          dayOfWeek: sqlBackUpTime?.timeType,
          startTime: sqlBackUpTime?.timeRange[0]?.slice(0, 5),
          endTime: sqlBackUpTime?.timeRange[1]?.slice(0, 5),
        }),
      })
    ])
  };

  const handleSave = () => {
    thisForm.validateFields().then(async (values: any) => {
      onChangeBackupSetting(values.sqlBackupStatus, values?.sqlBackupLocation, values?.sqlFlashBackSetting, values?.sqlBackUpTime)
        .then(() => {
          message.success(t('db.connection.conn.sqlBackupStatus.success'))

          getSqlBackupSetting();
        })
        .catch((err: any) => {
          console.error('备份配置失败', err)
        }).finally(() => {
          setBackupIsEdit(false)
        })
    })
  }

  const handleCancle = () => {
    setSqlBackupStatus(initSqlBackupValues?.sqlBackupStatus || false)
    thisForm.setFieldsValue({ ...initSqlBackupValues })
    setBackupIsEdit(false)
  }

  const renderLabel = () => {
    return (
      <Space>
        {t('db.connection.conn.sqlBackupStatus.label')}
        <Tooltip title={t('db.connection.conn.sqlBackupStatus.extra')}>
          <QuestionCircleOutlined />
        </Tooltip>
      </Space>
    );
  };

  const renderSQLFlashLabel = () => {
    return (
      <Space>
        {t('db.connection.conn.sqlFlashBackSetting.label')}
        <Tooltip
          title={t('db.connection.conn.sqlFlashBackSetting.extra')}
        >
          <QuestionCircleOutlined />
        </Tooltip>
      </Space>
    );
  };

  const handleChangeSqlBackupStatus = (status: boolean) => {
    setSqlBackupStatus(status)
  }

  const sqlBackUpTimeJson = ()=>{
    try{
      return JSON.parse(initSqlBackupValues?.sqlBackUpTime)
    } catch(e) {
      return null
    }
  }

  // 连接管理-连接配置页面
  const renderForm = () => {
    return (
      <Form
        form={thisForm}
        labelCol={{ span:  isLangEn ?  12 : 8 }}
        wrapperCol={{ span: 12 }}
        layout="horizontal"
      >
        <Row align='bottom'>
          <Col span={12}>
            <Form.Item  label={renderLabel()}>
              <Form.Item
                noStyle
                name="sqlBackupStatus"
                valuePropName="checked"
              >
                {
                  backupIsEdit 
                  ? <Switch onChange={(status) => handleChangeSqlBackupStatus(status)} />
                  : <span>
                      {
                        initSqlBackupValues?.sqlBackupStatus 
                        ? t('common.btn.enable') 
                        : t('common.btn.off')
                      }
                    </span>
                }
              </Form.Item>
            </Form.Item>
            {sqlBackupStatus && (
              <>
                <Form.Item
                  label={t('db.connection.conn.sqlBackupLocation')}
                  name="sqlBackupLocation"
                  dependencies={['sqlBackupStatus']}
                  rules={[{ required: true }]}
                >
                  {
                    backupIsEdit 
                    ? <SqlBackupLocationTree
                        dataSourceType={dataSourceType}
                        sqlBackupStatus={sqlBackupStatus}
                        initLocationInfo={initSqlBackupValues}
                      />
                    : <span>{initSqlBackupValues?.sqlBackupLocation}</span>
                  }
                </Form.Item>
                <Form.Item
                  label={t('db.connection.conn.sqlBackUpTime')}
                  name="sqlBackUpTime"
                  dependencies={['sqlBackupStatus']}
                  initialValue={initSqlBackupValues?.sqlBackUpTime}
                  rules={[{
                    validator(rule, value, callback) {
                      console.log(value, 'value')
                      if (_.isEmpty(value)) {
                        callback(t('db.connection.conn.sqlBackUpTime.hint'))
                      }
                      callback()
                    },
                  }]}
                >
                  {
                    backupIsEdit 
                      ? <CertiEffectiveTime />
                      : <span>
                          {t(`common.text.effectTimeTypeOption.${sqlBackUpTimeJson()?.day}`)}
                          {sqlBackUpTimeJson()?.startTime}-{sqlBackUpTimeJson()?.endTime}
                        </span>
                  }
                </Form.Item>
                <Form.Item  label={renderSQLFlashLabel()}>
                  <Form.Item
                    noStyle
                    name="sqlFlashBackSetting"
                    valuePropName="checked"
                  >
                    {
                      backupIsEdit 
                      ? <Switch />
                      : <span>
                          {
                            initSqlBackupValues?.sqlFlashBackSetting 
                            ? t('common.btn.enable') 
                            : t('common.btn.off')
                          }
                        </span>
                    }
                  </Form.Item>
                </Form.Item>
              </>
            )}
          </Col>
          <Col span={10}>
            <Form.Item>
              {
                backupIsEdit ?
                  <>
                    <Button type="primary" onClick={handleSave}>
                      {t('common.btn.save')}
                    </Button>
                    <Button 
                      className={styles.ml10} 
                      onClick={()=>{
                        handleCancle()
                        setBackupIsEdit(false)
                      }}
                    >
                      {t('common.btn.cancel')}
                    </Button>
                  </>
                : (
                  canEdit 
                    ? <span className='options' onClick={()=> setBackupIsEdit(true)}>{t('common.btn.edit')}</span>
                    : <Tooltip title={t('db.connection.noPerm',{roleNameList: permissionlist?.roleNameList?.join(',')})}>
                        <span className='disabled'>{t('common.btn.edit')}</span>
                      </Tooltip>
                )
              }
            </Form.Item>
          </Col>
        </Row>
      </Form>
    )
  }

  const layoutTmp = {
    labelCol: { span: isLangEn ? 8 : 5},
    wrapperCol: { span: 7 }
  }

  const renderFormItems = () => {
    return (
      <>
        <Form.Item  {...layoutTmp} label={renderLabel()}>
          <Form.Item
            noStyle
            name="sqlBackupStatus"
            valuePropName="checked"
          >
            <Switch onChange={(status) => setSqlBackupStatus(status)} disabled={isArchiveConnection} />
          </Form.Item>
        </Form.Item>
        {sqlBackupStatus && (
          <>
            <Form.Item
              label={t('db.connection.conn.sqlBackupLocation')}
              name="sqlBackupLocation"
              dependencies={['sqlBackupStatus']}
              rules={[{ required: true }]}
              {...layoutTmp}
            >
              <SqlBackupLocationTree
                dataSourceType={dataSourceType}
                sqlBackupStatus={sqlBackupStatus}
                initLocationInfo={initSqlBackupValues}
                isArchiveConnection={isArchiveConnection}
              />
            </Form.Item>
            <Form.Item
              label={t('db.connection.conn.sqlBackUpTime')}
              name="sqlBackUpTime"
              dependencies={['sqlBackupStatus']}
              rules={[{
                required: true,
                validator(rule, value, callback) {
                  if (_.isEmpty(value)) {
                    callback(t('db.connection.conn.sqlBackUpTime.hint'))
                  }
                  callback()
                },
              }]}
              {...layoutTmp}
              wrapperCol={{ span: 9 }}
            >
              <CertiEffectiveTime disabled={isArchiveConnection}/>
            </Form.Item>
            <Form.Item {...layoutTmp} label={renderSQLFlashLabel()}>
              <Form.Item
                noStyle
                name="sqlFlashBackSetting"
                valuePropName="checked"
               
              >
                <Switch disabled={isArchiveConnection} />
              </Form.Item>
            </Form.Item>
          </>
        )}
      </>
    )
  }

  return (
    <>
      {!operationType && (
        <div className={classnames(styles.settingTitle, styles.mt20)}>
          {t('db.connection.conn.backupConfig')}
        </div>
      )}

      {
        (!!operationType && operationType === "connectionConfig") ? renderFormItems() : renderForm()
      }

    </>
  );
});
