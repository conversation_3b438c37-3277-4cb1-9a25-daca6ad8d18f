/**
 * 连接管理
 */
import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react'
import { useTranslation } from 'react-i18next';
import { ResizableBox, ResizableProps } from "react-resizable"
import { useLocation } from 'react-router-dom';
import { Input, Tabs, Menu, Dropdown, Tooltip, message, Modal } from 'antd'
import { SearchOutlined } from '@ant-design/icons'
import { SimpleBreadcrumbs, Iconfont } from 'src/components'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import {
  queryGroupNodes,
  queryConnectionForLikeQuery,
  queryGroupConnections,
  queryDatasourceConnectionsByPage,
  getConnectionConfigItems,
  testConnection,
  deleteConnection,
  addApproverGroup,
  IAddApproverGroupParams,
  checkApproverGroupBingStatus,
  bindApproverGroupConn,
  bindApproverGroupUser,
  updateApproverGroupDetail,
  DataSourceType
} from "src/api";
import { GloablSearchLocationState } from 'src/pageTabs/GlobalSearchModal/WorkOrderResult';
import ChooseConnectionManager from './components/ChooseConnectionManage'
import ConnectionList from './ConnectionList'
import ConnectionTabs from './ConnectionTabs'
import TreeComponent from './TreeComponent'
import AddGroupModal from './AddGroupModal'
import UserAuthorizeList from "./UserAuthorizeList"
import {
  updateApproverGroupModalVisible,
  updateCurrentApproverGroupInfo,
  updateCurEditApproverType,
  setConnectionCurState
} from './connectionManagementPageSlice';
import { setGuideSteps, resetGuideSteps } from 'src/pageTabs/SceneGuideModal/guideSlice';
import CreateOrEditConnectionModal from "./CreateOrEditConnectionModal"
import classnames from 'classnames'
import { getCurrentModulePermissionByUrl } from 'src/util'
import ApproverGroupView from './ApproverGroupView';
import styles from './index.module.scss'
import ApproverGroupDetail from './ApproverGroupDetail';
import { showModal } from "../../store/extraSlice/modalVisibleSlice";
import { BatchCreateConnectionModal } from "../queryPage/sdt/modals";
import MarkModal from './components/MarkModal'
import ApproverGroupModal from './ApproverGroupModal';

const ResizableBoxProps: ResizableProps = {
  axis: 'x',
  width: 320,
  height: 0,
  minConstraints: [260, 0],
  maxConstraints: [520, 0],
}

interface IRefTypeProps {
  handleTestConnection: (args: any) => void;
  handleDeleteConnection: (args: any) => void;
}

interface IApproverRef {
  refreshUserList: () => void;
  refreshConnList: () => void;
}

export interface IRefApproverProps {
  onCreateApproverGroup: (type?: 'addConnection') => void;
  handleEditApproverGroupFieldInfo: (Record: any, type: 'users' | 'connections') => void;
}

const ConnectionManagementPage = () => {
  const { t } = useTranslation()
  const ref: React.RefObject<IRefTypeProps> = React.createRef();
  //审批人组详情
  const approverRef: React.RefObject<IApproverRef | null | undefined> = React.useRef();
  const { guideUniqueIds } = useSelector(state => state.guide);
  const { approverGroupModalVisible, currentApproverGroupInfo, curEditApproverType } = useSelector((state) => state.connectionManagement);
  const location = useLocation();
  const { state = {} } = location as { state: GloablSearchLocationState }
  const [rightWrapWidth, setRightWrapWidth] = useState<string>("");
  const [showContentType, setShowContentType] = useState<string>("list");
  const [connectionData, setConnectionData] = useState({});
  const [addGroupVisible, setAddGroupVisible] = useState(false);
  const [connectionModalVisible, setConnectionModalVisible] = useState(false);
  const [optionType, setOptionType] = useState<string>("");
  const [groupInfo, setGroupInfo] = useState({});
  const [selectNodeType, setSelectNodeType] = useState<string>("");
  const [selectId, setSelectId] = useState<string>("");
  const [curTabs, setCurTabs] = useState<string>("group");
  const [searchValue, setSearchValue] = useState<string>("");
  const [dataSourceType, setDataSourceType] = useState<DataSourceType>();
  const [connectionName, setConnectionName] = useState<string>("");
  const [connectionId, setConnectionId] = useState<string>("");
  const [selectedNodeInfo, setSelectedNodeInfo] = useState<any>();

  //审批人组当前选中节点信息
  const [selectedApproverGroupNode, setSelectedApproverGroupNode] = useState<any>(null);
  const [isRefreshApproverGroup, setIsRefreshApproverGroup] = useState(false);

  //新建连接 需单独存储datasourceType，不能与tree有混淆 CQ-3019
  const [createLinkDatasourceType, setCreateLinkDatasourceType] = useState<DataSourceType>();
  const { dataSourceList } = useSelector((state) => state.dataSource);
  // 判断 该登录权限下,该模块 只读/可编辑
  const { permissionList } = useSelector((state) => state?.login)
  const isReadOnly = permissionList?.DATABASE_MANAGEMENT?.CONNECTION_MANAGEMENT?.isOnlyRead
  const roleNameList = permissionList?.DATABASE_MANAGEMENT?.CONNECTION_MANAGEMENT?.roleNameList

  const [connectionManagerId, setConnectionManagerId] = useState<string>();
  const [visibleGuideMark, setVisibleGuideMark] = useState<boolean>(false);
  const [visibleConnectionDatabase, setVisibleConnectionDatabase] = useState<boolean>(false);
  const [isFirstBuildConnection, setIsFirstBuildConnection] = useState<boolean>(false);
  const [isBuildConnection, setIsBuildConnection] = useState<boolean>(false);
  const [connectListSearchValue, setConnectListSearchValue] = useState<any>() //模糊搜索的值
  const [connectionIds, setConnentionIds] = useState<string[]>([])

  //保存删除弹框modal 用来更新loading效果
  const deleteModalRef = useRef<any>();

  const initPagination = { pageNo: 1, pageSize: 10 }
  const initConnectionStatusParams = { isProblemConnection: false, isArchiveConnection: false }

  const [queryGroupConnectionsParams, setQueryGroupConnectionsParams] = useState<any>({ rest: { ...initPagination, ...initConnectionStatusParams } }) // 分组视图-组层级，获取连接列表参数
  const [queryConnectionForLikeQueryParams, setQueryConnectionForLikeQueryParams] = useState<{
    connectionIds: string[],
    likeQueryFlag?: boolean,  // 是否模糊搜索
    queryKeyWords?: string
  }>({
    connectionIds: [],
    ...initConnectionStatusParams,
  })

  const [datasourceConnectionsParams, setDatasourceConnectionsParams] =
    useState<{
      datasource: string,
      pageNo: string | number,
      pageSize: string | number,
      likeQueryFlag?: boolean,
      queryKeyWords?: string,
      isProblemConnection?: boolean,
      isArchiveConnection?: boolean,
    }>({
      datasource: '',
      ...initPagination,
      ...initConnectionStatusParams,
    })

  const dispatch = useDispatch()
  const BatchCreateConnectionModalVisible = useSelector(
    (state) => state.modal['BatchCreateConnectionModal']?.visible,
  )
  const connectionCurState = useSelector(
    (state) => state.connectionManagement['connectionCurState']
  )

  // 左侧treeData
  const {
    data: { total = 0, nodeList = [] } = {},
    refresh: refreshTree,
    run: queryNodes,
  } = useRequest(queryGroupNodes, {
    manual: true,
  });

  //删除连接操作
  const {run: runDeleteConnection, loading: deleteConnLoading} = useRequest(deleteConnection, { manual: true})

  useEffect(() => {
    deleteModalRef?.current?.update({
      okButtonProps: {
        loading: deleteConnLoading,
      },
    });
  }, [deleteConnLoading, deleteModalRef?.current]);


  useEffect(() => {
    if (guideUniqueIds?.length && guideUniqueIds.includes('CONNECTION_MANAGEMENT_ADD')) {
      setVisibleGuideMark(true);
      dispatch(setGuideSteps({ steps: [], guideUniqueId: 'CONNECTION_MANAGEMENT_ADD' }))
    }
    if (guideUniqueIds?.length && guideUniqueIds.includes('CONNECTION_MANAGEMENT_DETAIL') && isBuildConnection) {
      setIsFirstBuildConnection(true);
      dispatch(setGuideSteps({ steps: [], guideUniqueId: 'CONNECTION_MANAGEMENT_DETAIL' }))
    }
  }, [guideUniqueIds, isBuildConnection])

  //全局搜索
  useEffect(() => {

    if (state?.globalSearchTabKey && state?.globalSearchSubTabKey && nodeList?.length) {
      const curDataSouceTypeNode = nodeList.find((item: any) => item.id === state?.object?.dataSourceType)
      setCurTabs(state?.globalSearchTabKey);
      if (curDataSouceTypeNode && state?.globalSearchTabKey === 'group') {

        dispatch(setConnectionCurState(state.globalSearchSubTabKey))
        handleTreeSelect([state?.object?.dataSourceType],
          { node: curDataSouceTypeNode, selectedNodes: [curDataSouceTypeNode], selected: true }
        )
      }
    }
  }, [JSON.stringify(state), state?.globalSearchSubTabKey, nodeList, curTabs])

  const updateConnectionStatusParams = useCallback((connectionState: string) => {
    let newParams = {}
    switch (connectionState) {
      case 'allConnectionsTab': // 活跃连接 tab
        newParams = { isProblemConnection: false, isArchiveConnection: false }
        break;
      case 'problemConnectionsTab': // 问题连接 tab
        newParams = { isProblemConnection: true, isArchiveConnection: false }
        break;
      case 'archiveConnectionTab': // 归档连接 tab
        newParams = { isProblemConnection: false, isArchiveConnection: true }
        break;
    }
    const { nodeType } = selectedNodeInfo || {}
    if (curTabs === 'group') {
      if (nodeType === 'datasource' || !nodeType) {
        setDatasourceConnectionsParams((p: any) => {
          return { ...p, ...newParams, pageNo: 1 }
        })
      } else if (nodeType === 'group') {
        setQueryGroupConnectionsParams((p: any) => {
          return { ...p, rest: { ...(p?.rest || {}), ...newParams, pageNo: 1 } }
        })
      }
    } else if (curTabs === 'instance') {
      setQueryConnectionForLikeQueryParams((p: any) => {
        return { ...p, ...newParams, pageNo: 1 }
      })
    }
  }, [curTabs, selectedNodeInfo])

  useEffect(() => {
    setConnectionData([])
    updateConnectionStatusParams(connectionCurState)
  }, [connectionCurState])

  useEffect(() => {
    queryNodes(false);
  }, [queryNodes]);

  useEffect(() => {
    handleLeftWrapResize();
  }, []);

  // 连接列表的模糊搜索
  useEffect(() => {

    if (connectListSearchValue)
      if (curTabs === 'group') {
        if (selectNodeType === 'group') {
          setQueryGroupConnectionsParams({
            selectId: queryGroupConnectionsParams?.selectId,
            rest: {
              ...(queryGroupConnectionsParams?.rest || {}),
              queryKeyWords: connectListSearchValue
            }
          })
        } else {
          setDatasourceConnectionsParams({ ...datasourceConnectionsParams, likeQueryFlag: true, queryKeyWords: connectListSearchValue })
        }
      }
      else {
        if (connectionIds.length > 0) {
          setQueryConnectionForLikeQueryParams((p: any) => {
            return {
              ...p,
              connectionIds: connectionIds,
              likeQueryFlag: true,
              queryKeyWords: connectListSearchValue,
            }
          })
        }
      }
    else {
      if (curTabs === 'group') {
        if (selectNodeType === 'group') {
          setQueryGroupConnectionsParams({
            selectId: queryGroupConnectionsParams?.selectId,
            rest: {
              ...(queryGroupConnectionsParams?.rest || {}),
              queryKeyWords: ''
            }
          })
        } else {
          setDatasourceConnectionsParams((p: any) => {
            return { ...p, likeQueryFlag: false, queryKeyWords: undefined }
          })
        }
      }
      else {
        if (connectionIds.length > 0) {
          // 实例视图数据获取
          setQueryConnectionForLikeQueryParams((p: any) => {
            return {
              ...p,
              connectionIds: connectionIds,
              likeQueryFlag: false,
              queryKeyWords: '',
            }
          })
        }
      }
    }
  }, [connectListSearchValue, connectionIds, curTabs])

  useEffect(() => {
    setSelectedNodeInfo((info: any) => {
      if (info?.nodeType === 'datasource' || !info) {
        getDatasourceConnections(datasourceConnectionsParams)
      }
      return info
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [datasourceConnectionsParams])

  useEffect(() => {
    setSelectedNodeInfo((info: any) => {
      if (info?.nodeType === 'group') {
        const { selectId, rest } = queryGroupConnectionsParams || {}
        getGroupConnections(selectId, rest)
      }
      return info
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryGroupConnectionsParams])

  useEffect(() => {
    setCurTabs((tabKey: string) => {
      if (tabKey === 'instance') {
        getConnectionForLikeQuery(queryConnectionForLikeQueryParams)
      }
      return tabKey;
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryConnectionForLikeQueryParams])

  const handleLeftWrapResize = () => {
    // @ts-ignore
    const lWidth = document.getElementsByClassName("react-resizable-connection")[0]?.style.width ||
      "320px";
    const width = `calc(100vw - 30px - ${lWidth} )`;
    setRightWrapWidth(width);
  };

  // 根据数据源查连接
  const { run: getDatasourceConnections, loading: loadingDatasourceConnections } = useRequest(
    queryDatasourceConnectionsByPage,
    {
      manual: true,
      onSuccess: (res = []) => {
        setConnectionData(res);
      }
    },
  );

  // 根据组查询连接
  const { run: getGroupConnections, loading: loadingGroupConnections } = useRequest(queryGroupConnections, {
    manual: true,
    onSuccess: (res) => {
      setConnectionData(res);
    },
  });

  // 根据connectionIds查询连接实例
  const { run: getConnectionForLikeQuery, loading: loadingConnectionForLikeQuery } = useRequest(queryConnectionForLikeQuery, {
    manual: true,
    onSuccess: (res) => {
      setConnectionData(res);
    },
  });

  // 右侧展示内容
  const handleShowContentChange = (type: string, record?: any) => {
    setShowContentType(type);
    if (record) {
      setConnectionId(record?.connectionId);
      setConnectionName(record?.connectionName);
    }
  };

  // 右侧展示内容
  const handleChangeShowConnTabsContent = (type: string, record?: any) => {
    setShowContentType(type);
    if (record) {
      setConnectionId(record?.connectionId);
      setConnectionName(record?.connectionName);
      setDataSourceType(record?.connectionType)
    }
  };

  const findNodeById = (tree: any[], id: string, type?: 'single' | 'children'): any => {
    for (const node of tree) {
      if (node.id === id) {
        return type === 'single' ? node : node?.children; // 找到匹配的节点，返回它
      }

      if (node.children && node.children.length > 0) {
        const result = findNodeById(node.children, id); // 递归调用以在子节点中查找
        if (result) {
          return result;
        }
      }
    }
    return null;
  }

  // 刷新所有数据
  const handleRefresh = () => {
    return refreshTree().then((res: any) => {
      const { nodeList: currentNodeList = [] } = res || {};
      getConnectionList(selectNodeType, selectId, findNodeById(currentNodeList, selectId), currentNodeList);
    });
  };

  // tabs切换
  const handleTabChange = (activeKey: string) => {
    setConnectListSearchValue(undefined)
    setCurTabs(activeKey);
    setSearchValue(""); // 重置搜索值
    setSelectNodeType("datasource"); // 重置左侧树选择类型、默认数据源
    setShowContentType("list"); // 重置右侧展示内容
  };

  // 搜索值
  const handleSearch = (e: any) => {
    const value = e.target.value;
    setSearchValue(value?.trim());
  };

  // 新增组
  const handleAddGroup = (groupInfo: any) => {
    setGroupInfo(groupInfo);
    setAddGroupVisible(true);
  };

  //批量设置管理员
  const handleSettingManager = (id: string) => {
    setConnectionManagerId(id)
  }

  // 组新增连接
  const handleGroupConnection = (groupInfo: any) => {
    setGroupInfo(groupInfo);
    setAddGroupVisible(true);
  };

  // 创建连接
  const handleCreateConnection = (dataSourceName: DataSourceType) => {
    setOptionType("add");
    setConnectionModalVisible(true);
    // setDataSourceType(dataSourceName);
    setCreateLinkDatasourceType(dataSourceName)
  };

  // 编辑连接
  const handleEditContent = (record: any) => {
    setOptionType("edit");
    setConnectionId(record?.connectionId);
    setConnectionModalVisible(true);
  };

  // 复制连接
  const handleCopyContent = (record: any) => {
    // const { connectionType: dataSourceName } = record || {}
    setOptionType("copy");
    setConnectionModalVisible(true);
    setConnectionId(record?.connectionId);
  };

  /**
   *  删除连接
   */
  const handleDeleteConnection = useCallback((record: any) => {
 
    deleteModalRef.current = Modal.confirm({
      centered: true,
      content: <><button className={styles.confirmIcon} onClick={() => {  deleteModalRef.current.destroy() }}>x</button>{t('db.connection.delete.archiveConnection.tip')}</>,
      okText: t('common.btn.confirm'),
      cancelText: t('common.message.operateSuccessfully'),
      okButtonProps: {
        loading: false,
      },
      onOk: async() => {
        await delDeleteConnection(record);
      },
      onCancel: () => {
        testConnection(record).then(() => {
          message.success(t('common.message.operateSuccessfully'));
        }).catch((error) => {
          console.error('测试连接 Error occurred:', error);
        })
          .finally(() => {
            handleRefresh();
          })
        return Promise.reject(new Error(t('db.connection.keepOpenDialog')));  // 阻止关闭弹窗
      },
    })
  }, [deleteConnLoading])

  const delDeleteConnection = async(data: any) => {
    const { connectionId, connectionType } = data
    const params: any = {
      connectionId,
      connectionType,
      nodePath: '',
      nodeType: 'connection',
    }
    return runDeleteConnection(params)
      .then(() => {
        message.success(t('common.message.delete_success'))
        
        refreshTree();
        
        const currentConnectionState = connectionCurState;
        const { nodeType } = selectedNodeInfo || {};
        
        // 根据当前状态强制刷新相应的连接列表
        if (curTabs === 'group') {
          if (nodeType === 'datasource' || !nodeType) {
            // 数据源视图：获取当前状态的参数并刷新
            const currentParams = {
              ...datasourceConnectionsParams,
              // 根据当前连接状态设置正确的参数
              isProblemConnection: currentConnectionState === 'problemConnectionsTab',
              isArchiveConnection: currentConnectionState === 'archiveConnectionTab'
            };
            getDatasourceConnections(currentParams);
          } else if (nodeType === 'group') {
            // 组视图：获取当前状态的参数并刷新
            const { selectId, rest } = queryGroupConnectionsParams || {};
            const currentParams = {
              selectId,
              rest: {
                ...rest,
                // 根据当前连接状态设置正确的参数
                isProblemConnection: currentConnectionState === 'problemConnectionsTab',
                isArchiveConnection: currentConnectionState === 'archiveConnectionTab'
              }
            };
            getGroupConnections(currentParams.selectId, currentParams.rest);
          }
        } else if (curTabs === 'instance') {
          // 实例视图：获取当前状态的参数并刷新
          const currentParams = {
            ...queryConnectionForLikeQueryParams,
            // 根据当前连接状态设置正确的参数
            isProblemConnection: currentConnectionState === 'problemConnectionsTab',
            isArchiveConnection: currentConnectionState === 'archiveConnectionTab'
          };
          getConnectionForLikeQuery(currentParams);
        }
        
        // 解决删除节点本身的问题，选中父亲节点
        const node = findNodeById(nodeList, selectedNodeInfo?.parentId, 'single')
        if (node) {
          handleTreeSelect([{}], {
            selected: true,
            node,
            selectedNodes: [node]
          })
        }
      })
      .catch((err: any) => {
        console.error('删除失败', err)
      })
  }

  /**
   * 树选择
   */
  const handleTreeSelect = (_: any[], info: any) => {
    if (!info.selected) {
      return;
    }
    const selectId = info.node.id;
    const groupDatasourceName = info.node.parentId;
    const nodeType = info?.selectedNodes?.[0]?.nodeType;
    const nodeName = info?.selectedNodes?.[0]?.nodeName;
    if (nodeType === "datasource") {
      setDataSourceType(selectId);
    } else if (nodeType === "group") {
      // 解决实例视图组下datasource读取不到的问题
      setDataSourceType(groupDatasourceName);
    } else {
      const dataSourceType =
        info?.selectedNodes?.[0]?.connection?.connectionType;
      setDataSourceType(dataSourceType);
    }
    if (nodeType === "connection") {
      setConnectionName(nodeName);
      setConnectionId(selectId);
    }
    const showContentType = nodeType === "connection" ? "tabs" : "";
    setSelectId(selectId);
    setSelectNodeType(nodeType);
    setSelectedNodeInfo(info?.node);
    setShowContentType(showContentType); // 切换右侧展示内容
    setConnectListSearchValue(undefined); //重置搜索内容
    setDatasourceConnectionsParams((p: any) => {
      return { ...p, ...initPagination }
    })
    getConnectionList(nodeType, selectId, info.node?.children);
  };

  /**
 * 测试连接
 * 如果右边打开的是当前数据源且列表的情况，那么调用子组件的handleTestConnection实现状态同步
 * 如果右边打开的不是列表，那么先获取当前用户输入的数据，在手动调用测试连接接口
 */
  const handleTestConent = (id: string, sourceType: string) => {
    if (selectNodeType !== 'connection' && sourceType === dataSourceType) {
      ref?.current?.handleTestConnection({ connectionId: id });
    } else {
      getConnectionConfigItems(id).then(res => {
        const userInputs = {} as any;
        if (Array.isArray(res)) {
          res.forEach(item => {
            const { value, field } = item || {};
            if (field !== 'password') {
              userInputs[field] = value;
            }
            //默认值设为空字符串
            if (['authDatabase', 'userName'].includes(field)) {
              userInputs[field] = value || '';
            }
          })
        }
        testConnection({
          connectionId: Number(id),
          dataSourceType,
          userInputs,
        }).then(res => message.success(t('db.connection.available')));
      })
    }
  }

  /**
   * 初始化默认树选项
   */
  const handleTreeDefaultSelect = (item: any) => {
    const { id, nodeType, nodeName } = item || {};
    const showContentType = nodeType === "connection" ? "tabs" : "";
    setSelectId(id);
    setSelectNodeType(nodeType);
    setDataSourceType(nodeName);
    setShowContentType(showContentType); // 切换右侧展示内容
    setDatasourceConnectionsParams((p: any) => {
      return { ...p, ...initPagination }
    })
    getConnectionList(nodeType, id);
  };

  // 获取数据源和组下面的连接id
  const getChildrenId = (data: any[]) => {
    let connectionIds: any[] = [];
    const getId = (data: any[]) => {
      data?.forEach((i: any) => {
        if (i?.nodeType === "connection") {
          connectionIds.push(i?.id);
        } else if (i?.children) {
          getId(i?.children);
        }
      });
    };
    getId(data);
    return connectionIds;
  };

  // 实例试图从左侧树构造连接数据 接口请求
  const customConnectionList = (
    nodeType: string,
    selectId: string,
    children?: any[],
    list?: any[]
  ) => {
    let connectionIds = [];
    let currentList = Array.isArray(list) ? list : nodeList
    if (nodeType === "group") {
      connectionIds = (children || [])?.map((i: any) => i?.id);
    } else {
      const matchChildren = currentList?.filter(
        (i: any) => i?.id === selectId && i?.nodeType === nodeType
      )?.[0]?.children;
      connectionIds = getChildrenId(matchChildren);
    }
    setConnentionIds(connectionIds)
  };

  /**
   * 连接数据查询
   * 不要使用useEffect去管理该方法，nodeType、selectId两个参数都改变时，没发确保两个参数同时更新
   */
  const getConnectionList = (
    nodeType: string,
    selectId: any,
    children?: any[],
    list?: any[]
  ) => {
    if (curTabs === "group") {
      if (nodeType === "datasource") {
        setDatasourceConnectionsParams((p: any) => {
          return { ...p, datasource: selectId }
        })
      } else if (nodeType === "group") {
        setQueryGroupConnectionsParams((p: any) => {
          return { ...p, selectId: selectId }
        })
      }
    } else {
      customConnectionList(nodeType, selectId, children, list);
    }
  };

  const handleAddGroupModalClose = () => {
    setAddGroupVisible(false);
  };

  const handleConnectioModalClose = () => {
    setConnectionModalVisible(false);
  };

  //模块权限查询
  const modulePermissionObj: {
    roleTypeList: string[], isOnlyRead: boolean; roleNameList: string[]
  } = useMemo(() => {
    // getCurrentModulePermissionByUrl中有根据路由和菜单映射找到对应权限
    if(location.pathname === '/connection_management'){
      return getCurrentModulePermissionByUrl(permissionList, 'DATABASE_MANAGEMENT')
    }
    // 其他路由不需要处理
    return {
      roleTypeList: [], isOnlyRead: true, roleNameList: []
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(permissionList), location.pathname])

  const renderRightContent = useMemo(() => {

    switch (showContentType) {
      // 概览tabs
      case "tabs":
        return (
          <ConnectionTabs
            connectionId={connectionId}
            connectionName={connectionName}
            dataSourceType={dataSourceType}
            handleShowContentChange={handleShowContentChange}
            handleEditContent={handleEditContent}
            permissionlist={modulePermissionObj}
            isFirstBuildConnection={isFirstBuildConnection}
            setIsFirstBuildConnection={setIsFirstBuildConnection}
            setIsBuildConnection={setIsBuildConnection}
          />
        );
      // 用户授权列表
      case "userAuthorize":
        return (
          <UserAuthorizeList
            handleShowContentChange={handleShowContentChange}
          />
        );
      // 连接列表
      default:
        if (curTabs === 'approver') {
          return <ApproverGroupDetail
            ref={approverRef}
            permissionlist={modulePermissionObj}
            selectedApproverGroupNode={selectedApproverGroupNode}
            handleShowContentChange={handleChangeShowConnTabsContent}
          />
        }
        return (
          <ConnectionList
            ref={ref}
            curTab={curTabs}
            connectionData={connectionData}
            loading={loadingDatasourceConnections || loadingGroupConnections || loadingConnectionForLikeQuery}
            selectNodeType={selectNodeType}
            dataSourceType={dataSourceType}
            selectedNodeInfo={selectedNodeInfo}
            handleEditContent={handleEditContent}
            handleCopyContent={handleCopyContent}
            setConnectListSearchValue={setConnectListSearchValue}
            handleShowContentChange={handleShowContentChange}
            handleRefreshTree={refreshTree}
            handleRefresh={handleRefresh}
            permissionlist={modulePermissionObj}
            handleDeleteConnection={handleDeleteConnection}
            setPagination={({ pageNo, pageSize }: any) => {
              if (selectNodeType === 'group') {
                setQueryGroupConnectionsParams({ selectId: queryGroupConnectionsParams?.selectId, rest: { ...(queryGroupConnectionsParams?.rest || {}), pageNo, pageSize } })
              } else {
                setDatasourceConnectionsParams((params: any) => {
                  return { ...params, pageNo, pageSize }
                })
              }

            }}
            connectListSearchValue={connectListSearchValue}
          />
        );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    connectionData,
    connectionId,
    connectionName,
    curTabs,
    dataSourceType,
    selectNodeType,
    showContentType,
    isFirstBuildConnection,
    loadingDatasourceConnections,
    loadingGroupConnections,
    loadingConnectionForLikeQuery,
    connectListSearchValue,
    selectedApproverGroupNode
  ]);

  const afterSubmitApprverGroupAction = () => {
    message.success(t('common.message.testSuccessfully'))
    dispatch(updateApproverGroupModalVisible(false));
    dispatch(updateCurrentApproverGroupInfo(null));
    dispatch(updateCurEditApproverType('new'));
  }

  const UpdateApproverGroupFetch = (values: any) => {

    //不同接口
    if (curEditApproverType === 'connections') {
      bindApproverGroupConn({
        groupId: values?.groupId,
        connectionIds: values.connectionIds
      }).then(() => {
        afterSubmitApprverGroupAction();
        //刷新
        approverRef?.current?.refreshConnList();
      })
    } else if (curEditApproverType === 'edit') {
      updateApproverGroupDetail(values).then(() => {
        afterSubmitApprverGroupAction();
        //刷新
        approverRef?.current?.refreshUserList();
        approverRef?.current?.refreshConnList();
        setIsRefreshApproverGroup(!isRefreshApproverGroup);
      });
    } else if (curEditApproverType === 'users') {
      bindApproverGroupUser({
        groupId: values?.groupId,
        userIds: values.userIds
      }).then(() => {
        afterSubmitApprverGroupAction();
        //刷新
        approverRef?.current?.refreshUserList();
      });
    } else {
      addApproverGroup(values).then(() => {
        afterSubmitApprverGroupAction();
        setIsRefreshApproverGroup(!isRefreshApproverGroup);

      });
    }
  }

  const onSubmitUpdateApproverGroupList = async (values: IAddApproverGroupParams & { groupId?: number }) => {
    //只有连接可以修改才调用这个接口
    let boundArr: any = [];
    if (values?.connectionIds?.length) {
      boundArr = await checkApproverGroupBingStatus({ connectionIds: values.connectionIds, groupId: values?.groupId });
    }

    if (boundArr?.length && curEditApproverType && ['new', 'edit', 'connections'].includes(curEditApproverType)) {
      Modal.confirm({
        title: t('db.connection.update.approverGroup', { boundArr: boundArr.join(',') }),
        centered: true,
        okText: t('common.btn.yes'),
        cancelText: t('common.btn.no'),
        onOk: () => {
          UpdateApproverGroupFetch(values)
        }
      })
    } else {
      UpdateApproverGroupFetch(values)
    }
  }

  // 创建连接下拉菜单的disabled
  const createConnectionDisabled = useMemo(()=>{
    return (
      // 如果用户是 DBA 或高级用户中的任意一个角色，并且当前不是只读环境，则显示创建连接按钮
      !modulePermissionObj?.roleNameList?.some(role => [t('common.text.dba'), t('common.text.senior')].includes(role))&& 
      // 自定义角色授权的模块，不需要限制操作，则显示创建连接按钮
      !modulePermissionObj?.roleTypeList?.some(item => item.startsWith('CUSTOM_SYSTEM'))
    )
  },[modulePermissionObj?.roleNameList, modulePermissionObj?.roleTypeList, t])

  const ResizeHandle = (
    <div className={styles.resizeHandle}>
      <Iconfont type="icon-handle-8"></Iconfont>
    </div>
  );

  const DataSourceMenu = (
    <Menu>
      {dataSourceList.map(({ dataSourceName }) => (
        <Menu.Item
          key={dataSourceName}
          onClick={() => {
            handleCreateConnection(dataSourceName);
            setVisibleConnectionDatabase(false);
            setVisibleGuideMark(false);
          }}
        >
          <Iconfont
            type={"icon-connection-" + dataSourceName}
            className="mr8"
            style={{ fontSize: 14 }}
          ></Iconfont>
          {dataSourceName}
        </Menu.Item>
      ))}
      <Menu.Item
        key="BatchCreate"
        onClick={() => dispatch(showModal('BatchCreateConnectionModal'))}
      >
        <Iconfont
          type="icon-batchcomputepiliangjisuan"
          className="mr8"
          style={{ fontSize: 14 }}
        ></Iconfont>
        {t('common.btn.batchCreate')}
      </Menu.Item>

      {!dataSourceList.length && (
        <Menu.Item key="NoDataSource" disabled>
          {t('db.connection.noDataSource')}
        </Menu.Item>
      )}
    </Menu>
  );

  const breadcrumbData = [
    { title: t('db.connection.title') },
    {
      title: t('db.connection.connection'),
    },
  ];

  return (
    <div className={styles.connectionManagementPageWrap} >
      <div className={styles.breadcrumbLine} id="">
        <SimpleBreadcrumbs items={breadcrumbData} />
        {
          !isReadOnly && visibleGuideMark &&
          <MarkModal
            title={null}
            top={'50px'}
            right={'150px'}
            content={t('db.connection.guide.content')}
            okText={t('db.connection.guide.okText')}
            cancelText={t('db.connection.guide.cancelText')}
            visible={visibleGuideMark}
            setVisible={setVisibleGuideMark}
            onCancel={() => { dispatch(resetGuideSteps()); }}
            next={() => {
              setVisibleConnectionDatabase(true);
              dispatch(resetGuideSteps());
            }}
          />
        }
        {
          visibleGuideMark || visibleConnectionDatabase ?
            <Dropdown
              overlay={DataSourceMenu}
              trigger={["click"]}
              disabled={createConnectionDisabled || isReadOnly}
              overlayClassName={styles.createConnectionOverlayStyle}
              visible={visibleConnectionDatabase}
              onVisibleChange={(e: any) => setVisibleConnectionDatabase(e)}
            >
              <span
                id="connectionManagementBreadcrumb"
                className={classnames(styles.addConnectionBtn, styles.zIndex999)}
                onClick={() => { setVisibleConnectionDatabase(true); setVisibleGuideMark(false); }}
              >
                <Iconfont type="icon-add-link" /> &nbsp;{t('db.connection.create')}
              </span>
            </Dropdown>
            :
            <Dropdown
              overlay={DataSourceMenu}
              trigger={["click"]}
              disabled={createConnectionDisabled || isReadOnly}
              overlayClassName={styles.createConnectionOverlayStyle}
            >
              {
                isReadOnly ?
                  <Tooltip title={t('db.connection.noPerm', { roleNameList: roleNameList?.join(',') })}>
                    <span
                      id="connectionManagementBreadcrumb"
                      className={classnames(styles.addConnectionBtn, styles.colorb8b8b8)}
                    >
                      <Iconfont type="icon-add-link" /> &nbsp;{t('db.connection.create')}
                    </span>
                  </Tooltip>
                  :
                  createConnectionDisabled ?
                    <Tooltip
                      title={t('db.connection.noPerm2')}
                    >
                      <span
                        id="connectionManagementBreadcrumb"
                        className={classnames(styles.addConnectionBtn, styles.colorb8b8b8)}
                      >
                        <Iconfont type="icon-add-link" /> &nbsp;{t('db.connection.create')}
                      </span>
                    </Tooltip>
                    :
                    <span
                      id="connectionManagementBreadcrumb"
                      className={styles.addConnectionBtn}
                    >
                      <Iconfont type="icon-add-link" /> &nbsp;{t('db.connection.create')}
                    </span>
              }
            </Dropdown>
        }
      </div>
      <div className={styles.contentWrap}>
        <ResizableBox
          className={classnames('react-resizable-connection', styles.resizableBox)}
          handle={ResizeHandle}
          onResize={handleLeftWrapResize}
          {...ResizableBoxProps}
        >
          <div className={styles.leftWrap}>
            <div className={styles.mb10} style={{ color: '#667084' }}>{t('db.connection.list')}{total}</div>
            <Input
              className={classnames(styles.searchBtn, styles.mb10)}
              prefix={<SearchOutlined />}
              placeholder={t('common.search.placeholder')}
              onChange={handleSearch}
              value={searchValue}
              allowClear
            />
            <Tabs onChange={handleTabChange} activeKey={curTabs}>
              <Tabs.TabPane tab={t('db.connection.sdt.group')} key="group" />
              <Tabs.TabPane tab={t('db.connection.sdt.instance')} key="instance" />
              <Tabs.TabPane tab={t('db.connection.sdt.approver')} key="approver" />
            </Tabs>
            {
              curTabs === 'approver' ?
                <ApproverGroupView
                  searchValue={searchValue}
                  permissionlist={modulePermissionObj}
                  isRefreshApproverGroup={isRefreshApproverGroup}
                  selectedApproverGroupNode={selectedApproverGroupNode}
                  onSelectApproverGroupNode={(node: any) => { setSelectedApproverGroupNode(node); setShowContentType('list') }}
                />
                :
                <TreeComponent
                  curTabs={curTabs}
                  data={nodeList}
                  searchValue={searchValue}
                  refreshTree={refreshTree}
                  handleTreeSelect={handleTreeSelect}
                  handleAddConnection={handleGroupConnection}
                  handleDeleteConnection={handleDeleteConnection}
                  handleEditContent={handleEditContent}
                  handleCopyContent={handleCopyContent}
                  handleTestConnection={handleTestConent}
                  handleCreateConnection={handleCreateConnection}
                  handleTreeDefaultSelect={handleTreeDefaultSelect}
                  handleAddGroup={handleAddGroup}
                  handleSettingManager={handleSettingManager}
                  permissionlist={modulePermissionObj}
                  connectionId={connectionId}
                  // 选中状态控制
                  selectedKey={selectedNodeInfo?.key}
                  isBuildConnection={isBuildConnection}
                />
            }
          </div>
        </ResizableBox>
        <div className={styles.rightWrap} style={{ width: rightWrapWidth }}>
          <div className={styles.contentStyle}>{renderRightContent}</div>
        </div>
      </div>
      {/* 新建分组 */}
      {addGroupVisible && (
        <AddGroupModal
          visible
          groupInfo={groupInfo}
          handleClose={handleAddGroupModalClose}
          refresh={handleRefresh}
        />
      )}
      {/* 新建、编辑连接 */}
      {connectionModalVisible && (
        <CreateOrEditConnectionModal
          visible
          optionType={optionType}
          connectionId={connectionId}
          dataSourceType={optionType === 'add' ? createLinkDatasourceType : dataSourceType}
          handleClose={handleConnectioModalClose}
          refresh={handleRefresh}
          setIsBuildConnection={setIsBuildConnection}
        />
      )}
      {BatchCreateConnectionModalVisible && <BatchCreateConnectionModal />}
      {
        connectionManagerId && <ChooseConnectionManager groupId={connectionManagerId} onCancel={() => setConnectionManagerId('')} />
      }
      {/* 新建|编辑审批人组modal */}
      {
        approverGroupModalVisible && (
          <ApproverGroupModal
            onSubmit={(values: any) => onSubmitUpdateApproverGroupList(values)}
            onCancel={() => {
              dispatch(updateApproverGroupModalVisible(false));
              dispatch(updateCurEditApproverType('new'));
              dispatch(updateCurrentApproverGroupInfo(null));
            }}
            record={currentApproverGroupInfo}
            curEditApproverType={curEditApproverType}
          />
        )
      }
    </div>
  );
}
export default ConnectionManagementPage