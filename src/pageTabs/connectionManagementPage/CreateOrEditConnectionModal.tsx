/**
 * 创建、编辑连接
 */
import React, { useEffect, useMemo, useState } from 'react'
import * as _ from 'lodash'
import { useTranslation } from 'react-i18next'
import { Modal, Form, message, Button, Switch, Collapse, Row, Col, Input } from 'antd'
import { useSelector, useDispatch, useRequest } from 'src/hook'
import {
  createConnectionConfig,
  editConnectionConfig,
  getConnectionConfigItems, // 同getdmsConnectionConfig
  validateConnectionConfig,
  generateConnectionUrl,
  getFilterResource,
  DataSourceType
} from 'src/api'
import { setConnectionResult, setIsTabEditConnection, setRefreshTabConnection, updateCurrentApproverGroupInfo } from 'src/pageTabs/connectionManagementPage/connectionManagementPageSlice'
import GenerateFormItem from './components/GenerateFormItem'
import styles from './index.module.scss'
import ConnectionSetting from './ConnectionSetting'
import { SelectWithAdd } from 'src/components/SelectWithAdd';
import { FormTailLayoutTwo } from 'src/constants/layout'
import UploadAndInput from './components/UploadAndInput'
import { parseFileNameFromPath } from 'src/util/connectionManage'

interface IProps {
  visible: boolean
  optionType: string
  dataSourceType: any
  [p: string]: any
}

//指定数据源 连接模式为集群是 控制端口地址与连接成员的切换
const DATASOUCE_AND_CLUSTER_MAPPING: {[type in any]: string[]} = {
  MySQL: ['mysqlGroupReplicationMode'],
  PolarDB: ['polarDbGroupReplicationMode'],
}
const CreateOrEditConnectionModal = (props: IProps) => {
  const {
    visible,
    optionType,
    dataSourceType,
    connectionId,
    handleClose,
    refresh,
    setIsBuildConnection,
  } = props

  const dispatch = useDispatch()
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const { Panel } = Collapse;
  const isLangEn = useSelector((state) => state.login.locales) === 'en'

  const { dataSourceMap } = useSelector((state) => state.dataSource)
  const { isTabEditConnection, currentApproverGroupInfo } = useSelector((state) => state.connectionManagement)  // 是否为tab编辑连接
  // isArchiveConnection 归档连接，不可编辑
  const { testConnection, isArchiveConnection } = useSelector((state) => state.connectionManagement)

  const OPERATION_TYPE_MAP = {
    'edit': t('common.btn.edit'),
    'add': t('db.connection.conn.create'),
    'copy': t('common.btn.duplication'),
  } as any
  
  const [confirmLoading, setConfirmLoading] = useState<boolean>(false)
  const [confirmBtnLoading, setConfirmBtnLoading] = useState<boolean>(false)
  const [testLoading, setTestLoading] = useState<boolean>(false)
  const [formItems, setFormItems] = useState<any[]>()
  const [hasChanedPassword, setHasChanedPassword] = useState(false)
  const [activeKey, setActiveKey] = useState<string[]>(['connectionInfo'])
  const [visibleSystemDatabaseForemItem, setVisibleSystemDatabaseForemItem] = useState<boolean>(true)

  // 获取数据源的过滤库
  const {
    data: filterResourceOptions = [],
    run: getFilterResourceRun,
  } = useRequest(getFilterResource, {
    manual: true,
    onSuccess(res) {
      form.setFieldsValue({
        filterResources: res?.map((item: string) => item)
      });
    }
  })

  useEffect(() => {
    form && getFilterResourceRun(dataSourceType)
  }, [dataSourceType, form])

  useEffect(() => {
    if (optionType === 'edit' && connectionId) {
      getConnectionConfigItems(connectionId).then((data) => {
        const items = data.filter(({ label }) => label !== t('db.connection.conn.monitorSwitch'))
        setFormItemsWrapper(items)
      })
    } else if (optionType === 'copy' && connectionId) {
      getConnectionConfigItems(connectionId).then((data) => {
        // 复制不知道需不需要这个字段，暂时留着
        // const items = data.filter(({ label }) => label !== '监控开关')
        // 复制时候，连接名称和备注h和url可以编辑
        const editFields = ['connectionName', 'remark', 'connectionUrlView', 'password'];
        setFormItemsWrapper(data.map(item => {
          const { field } = item;
          return editFields.includes(field) ? { ...item, value: '' } : { ...item };
        }))
      })
    } else {
      // @ts-ignore
      const items = dataSourceMap[dataSourceType]?.connectionForm || [];
      setFormItemsWrapper(items);
    }
  }, [connectionId, dataSourceMap, dataSourceType, optionType])
  const setFormItemsWrapper = (formItems: any) => {
    setFormItems(formItems);
    // feature: MongoDB 表单特殊处理
    if (dataSourceType === 'MongoDB') {
      const connectionMode = formItems.filter((formItem: any) => formItem.field === 'connectionMode')[0]?.value;
      const srv = formItems.filter((formItem: any) => formItem.field === 'srv')[0]?.value;
      const authenticationMode = formItems.filter((formItem: any) => formItem.field === 'authenticationMode')[0]?.value;
      // 深拷贝，复制可编辑的 formItems
      let editableFormItems = _.cloneDeep(formItems);
      // 调整 MongoDB 表格
      adjustMongoForm(editableFormItems, srv, connectionMode, authenticationMode);
      // 更新
      setFormItems(editableFormItems);
    }

    if (['MySQL','PolarDB'].includes(dataSourceType)) {
      const connectionMode = formItems.filter((formItem: any) => formItem.field === 'connectionMode')[0]?.value;
      // 深拷贝，复制可编辑的 formItems
      let editableFormItems = _.cloneDeep(formItems);
      // 调整 mysql 表格
      adjustMySQLForm(editableFormItems, connectionMode);
      // 更新
      setFormItems(editableFormItems);
    }
  }
  // 创建/编辑连接
  const handleSaveConnection = () => {
    const action =
      optionType === 'edit' ? editConnectionConfig : createConnectionConfig
    form.validateFields().then((values) => {
      let initParams = _.cloneDeep(values);
      initParams.dataSourceType = dataSourceType;
      if (dataSourceType === 'Hive') {
        initParams.keytab = initParams?.keytab?.path || '';
        initParams.krb5_config = initParams?.krb5_config?.path || '';
      }
      delete initParams?.openAllSchema;
      delete initParams?.isAdminWithAllPerms;
      const params: any = {
        dataSourceType,
        userInputs: initParams,
        connectionpoolUserInputs: {
          borrowMaxWaitMillis: initParams?.borrowMaxWaitMillis,
          maxSize: initParams?.maxSize,
          removeAbandonedTimeout: initParams?.removeAbandonedTimeout,
        },
        commitMode: initParams?.commitMode,
        connectionManager: initParams?.connectionManager,
        isAdminWithAllPerms: values?.isAdminWithAllPerms,// 是否管理员拥有所有权限
        // 连接管理 部分的字段，与 连接管理tab的字段名不一致
        sqlBackUpSwitch: initParams?.sqlBackupStatus || false, // 数据备份 switch
        sqlBackUpLocation: initParams?.sqlBackupLocation,  // 备份位置
        sqlFlashBackSwitch: initParams?.sqlFlashBackSetting || false, // 闪回功能 switch
        rowNum: Number(initParams?.pageRows) || 100, //查询时分页
        assigneeGroupId: optionType === 'copy' && currentApproverGroupInfo?.isInternal ? null: initParams?.assigneeGroupId,
        ...(initParams?.sqlBackupStatus ? 
          {
            sqlBackUpTime: {
              dayOfWeek: initParams?.sqlBackUpTime?.timeType,
              startTime: initParams?.sqlBackUpTime?.timeRange?.[0]?.slice(0,5),
              endTime: initParams?.sqlBackUpTime?.timeRange?.[1]?.slice(0,5)
            }
          } : {})
      }
      delete initParams?.borrowMaxWaitMillis;
      delete initParams?.maxSize;
      delete initParams?.removeAbandonedTimeout;
      delete initParams?.commitMode;
      delete initParams?.connectionManager;
      delete initParams?.sqlBackupStatus;
      delete initParams?.sqlBackupLocation;
      delete initParams?.sqlFlashBackSetting;

      // 自动回滚时间
      if(![null, undefined].includes(values?.implicitCommitTimeout)){
        params.connectionpoolUserInputs['implicitCommitTimeout'] = values?.implicitCommitTimeout
      }

      if (optionType === 'edit') {
        params.connectionId = connectionId

      } else {
        params.openAllSchema = values?.openAllSchema
        if (values?.openAllSchema) {
          params.filterResources = values?.filterResources
        }
      }
      setConfirmLoading(true)
      setConfirmBtnLoading(true)
      action(params, hasChanedPassword)
        .then(() => {
          message.success(t('db.connection.conn.connSuccess', {val: OPERATION_TYPE_MAP[optionType]}))
          refresh()
          if (optionType === "add") {
            setIsBuildConnection(true)
          }
          handleClose()
          if (isTabEditConnection) {
            dispatch(setRefreshTabConnection(true))
            dispatch(setIsTabEditConnection(false))
          }
        })
        .finally(() => {
          setConfirmLoading(false);
          setConfirmBtnLoading(false);
          dispatch(updateCurrentApproverGroupInfo(null));
        })
    })
  }

  // 测试连接
  const handleTestConnection = () => {
    form.validateFields().then((values) => {
      const params: any = {
        dataSourceType,
        userInputs: {
          ...values,
          keytab: dataSourceType === 'Hive' ? values?.keytab?.path || '' : undefined,
          krb5_config: dataSourceType === 'Hive' ? values?.krb5_config?.path || '' : undefined,
          //部分数据源不一定存在该字段
          ...('authDatabase' in values ? { authDatabase: values?.authDatabase ?? '' } : {}),
          ...('userName' in values ? { userName: values?.userName ?? '' } : {}),
        },
      };
      if (optionType === 'edit') {
        params.connectionId = connectionId
      }
      params.userInputs.dataSourceType = dataSourceType

      setTestLoading(true)
      validateConnectionConfig(params, hasChanedPassword)
        .then((res) => {
          // 有且一定是true
          const currentTestConnection = [...testConnection];
          const resTest = { connectionId, result: res };
          for (let i = 0; i < currentTestConnection.length; i++) {
            if (currentTestConnection[i].connectionId === connectionId) {
              currentTestConnection[i] = resTest
              break;
            }
            if (i === currentTestConnection.length - 1) {
              currentTestConnection.push(resTest)
            }
          }
          dispatch(setConnectionResult(currentTestConnection))
          res ? message.success(t('db.connection.conn.connAvailable')) : message.error(t('db.connection.conn.connFail'))
        })
        .catch(() => {
          const currentTestConnection = [...testConnection];
          const resTest = { connectionId, result: false };
          for (let i = 0; i < currentTestConnection.length; i++) {
            if (currentTestConnection[i].connectionId === connectionId) {
              currentTestConnection[i] = resTest
              break
            }
            if (i === currentTestConnection.length - 1) {
              currentTestConnection.push(resTest)
            }
          }
          dispatch(setConnectionResult(currentTestConnection))
        })
        .finally(() => {
          setTestLoading(false);
          // 测试连接的结果，ConnectionList与其同步
          refresh();
        })
    })
  }

  const onValuesChange = _.debounce((changedValues: any, allValues: any) => {
    if (changedValues.hasOwnProperty('password')) {
      setHasChanedPassword(true);
    }
    if (!changedValues.hasOwnProperty('connectionUrlView')) {
      const params: any = {
        dataSourceType,
        userInputs: {
          ...allValues,
          password: undefined,
          keytab: dataSourceType === 'Hive' ? allValues?.keytab?.path || '' : undefined,
          krb5_config: dataSourceType === 'Hive' ? allValues?.krb5_config?.path || '' : undefined,
          authDatabase: allValues?.authDatabase || '',
        }
      }
      generateConnectionUrl(params).then(res => {
        const { connectionUrlView } = res || {};
        if (res) {
          form.setFieldsValue({ connectionUrlView })
        }
      })
    }
    // feature: MongoDB 表单特殊处理
    if (dataSourceType === 'MongoDB') {
      const srv = allValues.srv;
      const connectionMode = allValues.connectionMode;
      const authenticationMode = allValues.authenticationMode;
      // 深拷贝，复制可编辑的 formItems
      let editableFormItems = _.cloneDeep(formItems);
      // 调整 MongoDB 表格
      adjustMongoForm(editableFormItems, srv, connectionMode, authenticationMode);
      // 更新
      setFormItems(editableFormItems);
    }
    if (['MySQL', 'PolarDB'].includes(dataSourceType)) {
      const connectionMode = allValues.connectionMode;
      // 深拷贝，复制可编辑的 formItems
      let editableFormItems = _.cloneDeep(formItems);
      // 调整 mysql 表格
      adjustMySQLForm(editableFormItems, connectionMode);
      // 更新
      setFormItems(editableFormItems);
    }
  }, 300)

  // mongodb 表单特殊处理
  const adjustMongoForm = (formItems: any, srv: any, connectionMode: any, authenticationMode: any) => {
    // 根据变更隐藏相应的组件
    formItems?.forEach((formItem: any) => {
      checkFormHideForMongoDB(formItem, srv, connectionMode, authenticationMode)
    })
  }
  const adjustMySQLForm = (formItems: any, connectionMode: any) => {
    // 根据变更隐藏相应的组件
    formItems?.forEach((formItem: any) => {
      checkFormHideForMySQL(formItem, connectionMode)
    })
  }

  // 校验是否隐藏
  const checkFormHideForMongoDB = (formItem: any, srv: any, connectionMode: any, authenticationMode: any) => {
    // 连接成员: srv | 单机模式 隐藏
    if (formItem.field === 'connectionMembers') {
      formItem.hide = srv || (connectionMode === 'StandAlone')
    }
    // IP: 非srv | 非单机模式 隐藏
    if (formItem.field === 'connectionUrl') {
      formItem.hide = !(srv || connectionMode === 'StandAlone')
    }
    // 端口: srv | 非单机模式 隐藏
    if (formItem.field === 'connectionPort') {
      formItem.hide = srv || connectionMode != 'StandAlone'
    }
    // 复制集名称 和 读偏好: 非 Replica Set 隐藏 
    if (['replicaSet', 'readPreference'].includes(formItem.field)) {
      formItem.hide = connectionMode != 'ReplicaSet'
    }
    // 服务名 和 服务主体：非 Kerberos 隐藏
    if (['serviceName', 'serviceRealm'].includes(formItem.field)) {
      formItem.hide = authenticationMode != 'Kerberos'
    }
    // 用户名 和 密码 和 验证库：非 Password 隐藏
    if (['userName', 'password', 'authDatabase'].includes(formItem.field)) {
      formItem.hide = authenticationMode != 'Password'
    }
  }
  //通用 控制集群模式展示连接成员 ['MySQL', 'PolarDB']
  const checkFormHideForMySQL = (formItem: any, connectionMode: any) => {
    const clustersOfDataSource = dataSourceType ? DATASOUCE_AND_CLUSTER_MAPPING[dataSourceType] : [];
    const isCurCluster = clustersOfDataSource?.includes(connectionMode);

    // 连接成员: srv | 单机模式 隐藏
    if (formItem.field === 'connectionMembers') {
      formItem.hide = !(isCurCluster)
      formItem.required = (isCurCluster)
    }
    // IP: 非srv | 非单机模式 隐藏
    if (formItem.field === 'connectionUrl') {
      formItem.hide = (isCurCluster)
      formItem.required = !(isCurCluster)
    }
    // 端口: srv | 非单机模式 隐藏
    if (formItem.field === 'connectionPort') {
      formItem.hide = (isCurCluster)
    }
  }
  const onChange = (key: any) => {
    setActiveKey(key)
  };

  // 渲染表单项：是否开启所有资源开关
  const renderFormItemsOpenAllSchemaSwitch = () => {
    return (
      <Col span={isLangEn ? 24 : 12} key={'openAllSchema'} style={{ marginLeft: -13 }}>
        <Form.Item
          hidden={optionType === 'edit'}
          label={t('db.connection.conn.openAllSchema')}
          name='openAllSchema'
          labelCol={{span: 10}}
          wrapperCol={{span: 14}}
        >
          <Switch checkedChildren={t('common.btn.on')} unCheckedChildren={t('common.btn.off')} disabled={isArchiveConnection} defaultChecked onChange={(e) => {setVisibleSystemDatabaseForemItem(e)}}/>
        </Form.Item>
      </Col>
    )
  }

  const renderFormItemsSystemDatabase = useMemo(() => {
    return (
      <Col span={24} key={'filterResources'} style={{ marginLeft: -13 }}>
        <Form.Item
          hidden={optionType === 'edit'}
          label={t('db.connection.conn.filterResources')}
          name='filterResources'
          labelCol={{ span: isLangEn ? 13 : 6 }}
          wrapperCol={{ span: 18 }}
        >
          <SelectWithAdd
            form={form}
            fieldKey="filterResources"
            options={filterResourceOptions}
            maxTagCount={4}
            disabled={isArchiveConnection}
          />
        </Form.Item>
      </Col>
    )
  }, [optionType, form, filterResourceOptions, isArchiveConnection])

  // 渲染表单项：获取此连接所有访问权限
  const renderFormItemsGainAccessSwitch = (initialValue:boolean) => {
    return (
      <Col span={isLangEn ? 24: 12} key={'isAdminWithAllPerms'} style={{ marginLeft: -13 }}>
        <Form.Item
          label={t('db.connection.conn.isAdminWithAllPerms')}
          name='isAdminWithAllPerms'
          labelCol={{span: 10}}
          wrapperCol={{span: 14}}
          initialValue={initialValue}
        >
          <Switch checkedChildren={t('common.btn.enable')} disabled={optionType === 'edit' || isArchiveConnection} unCheckedChildren={t('common.btn.forbidden')} defaultChecked={initialValue}/>
        </Form.Item>
      </Col>
    )
  }

  // 验证模式不同选项下的表单渲染项
  const renderAuthModeFormItems = () => {
    return <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => prevValues?.authenticationMode !== currentValues?.authenticationMode} key='Hive_authMode'>
      {({ getFieldValue }) => !["Kerberos", 'Kerberos_ZK'].includes(getFieldValue("authenticationMode")) ?
        <>
          <Col span={12} key={'userName'}>
            <Form.Item
              label={t('db.connection.conn.userName')}
              name='userName'
              {...FormTailLayoutTwo}
            >
              <Input placeholder={t('db.connection.conn.userName.plac')} />
            </Form.Item>
          </Col>
          <Col span={12} key={'password'}>
            <Form.Item
              label={t('common.text.password')}
              name='password'
              {...FormTailLayoutTwo}
            >
              <Input placeholder={t('db.connection.conn.password.plac')} />
            </Form.Item>
          </Col>
        </> : <>
          <Col span={12} key={'principal'}>
            <Form.Item
              label={"Principal"}
              name='principal'
              {...FormTailLayoutTwo}
            >
              <Input placeholder={t('db.connection.conn.principal.plac')} />
            </Form.Item>
          </Col>
          <Col span={12} key={'keytab'}>
            <Form.Item
              label={"Keytab"}
              name='keytab'
              {...FormTailLayoutTwo}
            >
              <UploadAndInput placeholder={t('db.connection.conn.keytab.plac')} accept=".keytab" />
            </Form.Item>
          </Col>
          <Col span={12} key={'krb5_config'}>
            <Form.Item
              label={"Krb5 config"}
              name='krb5_config'
              {...FormTailLayoutTwo}
            >
              <UploadAndInput placeholder={t('db.connection.conn.krb5_config')}  accept=".conf"/>
            </Form.Item>
          </Col>
          {
            getFieldValue("authenticationMode") === "Kerberos_ZK" &&
            <Col span={12} key={'zk_principal'}>
              <Form.Item
                label={"ZK Principal"}
                name='zk_principal'
                {...FormTailLayoutTwo}
              >
                <Input placeholder={t('db.connection.conn.zk_principal.plac')} />
              </Form.Item>
            </Col>
          }
        </>
      }
    </Form.Item>
  }

  const renderFormItems = useMemo(() => {
    let renderedFormItems: any[] = []
    if (!!formItems) {
      let itemArr = formItems
      if(dataSourceType === 'Hive') {
        // Hive，数据库，以下字段自定义渲染，所以先筛除掉
        itemArr = formItems.filter((value: any) => !['principal', 'keytab', 'krb5_config','userName','password','zk_principal'].includes(value?.field))
      }
      let tmp: any
      // 将备注表单调到最后
      itemArr.filter((value: any) => !value?.hide)
        .map((value: any) => {
          let spanNum = value?.type === "radio" ? 24 : 12;
          let marginLeft = value?.type === "radio" ? '13%' : '0';
          let formItemSpec = {
            ...value,
            disabled: isArchiveConnection,
            value: value?.value || (value?.field === 'authenticationMode' ? 'Password' : null)
          }
          let item = (
            <Col span={spanNum} key={value?.field} style={{ marginLeft: marginLeft }}>
              <GenerateFormItem spec={formItemSpec} operationType="connectionInfo" />
            </Col>
          )

          if (value?.field === 'remark') {
            tmp = item
          }

          const newItems = value?.field === 'remark' ?
            renderFormItemsOpenAllSchemaSwitch()
            : value?.field === 'isAdminWithAllPerms' ?
              renderFormItemsGainAccessSwitch(optionType === 'edit' ? Boolean(value?.value) : true)
              : item
          renderedFormItems.push(newItems)

          if (value?.field === 'remark') {
            if (visibleSystemDatabaseForemItem && !["mongodb", "redis"].includes(dataSourceType?.toLowerCase())) {
              renderedFormItems.push(renderFormItemsSystemDatabase)
            }
          }
          // Hive 的 验证模式
          if (value?.field === 'authenticationMode' && dataSourceType === 'Hive') {
            const keytab = formItems?.find((item: any) => item.field === 'keytab')?.value
            const keytabFileName = keytab ? parseFileNameFromPath(keytab) : undefined
            const krb5_config = formItems?.find((item: any) => item.field === 'krb5_config')?.value
            const krb5_configFileName = krb5_config?parseFileNameFromPath(krb5_config):undefined
            const initialValues = {
              userName :formItems?.find((item: any) => item.field === 'userName')?.value,
              password :formItems?.find((item: any) => item.field === 'password')?.value,
              principal :formItems?.find((item: any) => item.field === 'principal')?.value,
              zk_principal :formItems?.find((item: any) => item.field === 'zk_principal')?.value,
              keytab : {
                fileName:keytabFileName,
                path: keytab
              },
              krb5_config : {
                fileName:krb5_configFileName,
                path: krb5_config
              }
            }
            form.setFieldsValue({
              ...initialValues           
            })
            renderedFormItems.push(renderAuthModeFormItems())
          }
        });
      renderedFormItems.push(tmp)
    }
    return <Row>{renderedFormItems}</Row>
  },[formItems, dataSourceType, JSON.stringify(filterResourceOptions), isArchiveConnection, optionType, visibleSystemDatabaseForemItem])

  const renderFooterButton = useMemo(() => {
    if (activeKey.length < 2 && activeKey[0] === 'connectionInfo') {
      return (
        [
          <Button onClick={handleTestConnection} loading={testLoading}>
            {t('db.connection.test.connection')}
          </Button>,
          <Button onClick={() => setActiveKey(['connectionConfig'])} type='primary'>
            {t('common.btn.next')}
          </Button>
        ]
      )
    } else if (activeKey[0] === 'connectionConfig' || activeKey.length === 2) {
      return (
        [
          <Button onClick={() => setActiveKey(['connectionInfo'])} loading={testLoading}>
            {t('common.btn.pre')}
          </Button>,
          !isArchiveConnection &&
          <Button onClick={() => handleSaveConnection()} type='primary' loading={confirmBtnLoading}>
            {t('common.btn.confirm')}
          </Button>
        ]
      )
    }
    return <></>
  }, [activeKey, testLoading, confirmBtnLoading])

  const renderModal = useMemo(() => {
    return (
      <Modal
        visible={visible}
        onCancel={handleClose}
        maskClosable={false}
        title={t('db.connection.conn.edit.title', {conn: `${OPERATION_TYPE_MAP[optionType]} ${dataSourceType}`})}
        confirmLoading={confirmLoading}
        className={styles.createOrEditConnectionWrap}
        width={1150}
        footer={renderFooterButton}
      >
        <Form
          form={form}
          autoComplete="off"
          onValuesChange={onValuesChange}
          className={styles.createOrEditConnectionForm}
          initialValues={{ openAllSchema: true }}
        >
          <Collapse bordered={false} activeKey={activeKey} onChange={onChange}>
            <Panel header={t('db.connection.conn.panel1')} key="connectionInfo">
              {renderFormItems}
            </Panel>
            <Panel header={t('db.connection.conn.panel2')} key="connectionConfig">
              <ConnectionSetting
                dataSourceType={dataSourceType}
                connectionId={optionType === "add" ? '' : connectionId}
                optionType={optionType}
                operationType="connectionSetting"
                form={form}
              />
            </Panel>
          </Collapse>
        </Form>
      </Modal>
    )
  },
    [
      Panel,
      confirmLoading,
      connectionId,
      dataSourceType,
      form,
      handleClose,
      onValuesChange,
      optionType,
      renderFooterButton,
      renderFormItems,
      visible,
    ])

  return (
    renderModal
  )
}

export default CreateOrEditConnectionModal
