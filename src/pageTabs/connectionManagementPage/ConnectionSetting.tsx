/**
 * 连接设置
 */
import React, { useEffect, useState } from 'react'
import * as _ from 'lodash';
import { useTranslation } from 'react-i18next';
import { Spin, Form, Radio, Button, message, Tooltip, Row, Col, Input } from 'antd'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import GenerateFormItem from './components/GenerateFormItem'
import ChooseManage from 'src/components/chooseManage'
import ConnectionSecretKeyPage from './ConnectionSecretKey'
import {
  getConnectionPoolConfig,
  getConnectionPoolDefaultConfig,
  getConnectionWorkConfig,
  updateConnectionPoolConfig,
  updateConnectionWorkConfig,
  getDataSourceDescription,
  getConnExecSqlRowNum,
  setConnExecSqlRowNum,
  getSqlBackupDT
} from 'src/api'
import classnames from 'classnames'
import styles from './index.module.scss'
import {
  updateCurEditApproverType,
  updateCurrentApproverGroupInfo,
  updateApproverGroupModalVisible
} from './connectionManagementPageSlice';
import ChooseApproverGroup from './components/ChooseApproverGroup';
import { ConnectionDataBackup } from './components/ConnectionDataBackup'

interface PropsI {
  [p: string]: any
}
const ConnectionSetting = (props: PropsI) => {

  const dispatch = useDispatch();
  const { t } = useTranslation();
  const isLangEn = useSelector((state) => state.login.locales) === 'en'
  const { connectionId, dataSourceType, permissionlist, canEdit, optionType, operationType, connectionPool } = props
  const { roleNameList } = permissionlist || {}
  const [form] = Form.useForm();
  const thisForm = (operationType && operationType === "connectionSetting") ? (props?.form) : form;
  const { isArchiveConnection } = useSelector((state) => state.connectionManagement) // 连接管理-编辑连接：归档连接不可编辑

  const [poolIsEdit, setPoolIsEdit] = useState(false)           // 连接池是否编辑
  const [pageRowIsEdit, setPageRowIsEdit] = useState(false)     // 查询时分页行数是否编辑

  //是否取消设置
  const [pageRows, setPageRows] = useState<any>(100) //分页行数
  //连接管理员是否更改
  const [hasModifiedConnAdmin, setHasModifiedConnAdmin] = useState(false);
  //false 不支持自动提交事物 隐藏事物
  const { data: hideTransactionSetting } = useRequest(() => dataSourceType && getDataSourceDescription(dataSourceType), {
    refreshDeps: [dataSourceType],
    formatResult: (res) => !res?.supportTransaction
  })

  // 获取连接管理中连接设置项中存在备份配置的数据源类型
  const { data: sqlBackupDT } = useRequest(getSqlBackupDT, {
    manual: false,
    formatResult: (res) => {
      return res?.map((item: string) => item.toLocaleLowerCase())
    }
  })

  // 获取查询时分页数
  const { refresh: refreshGetRowNum } = useRequest(() => getConnExecSqlRowNum({ connectionId }), {
    manual: false,
    onSuccess: (data: any) => {
      if (data && Number(data)) {
        setPageRows(Number(data))
        thisForm.setFieldsValue({ pageRows: Number(data) })
      }
      else {
        setPageRows(100)
        thisForm.setFieldsValue({ pageRows: 100 })
      }
    },
    onError: (err) => {
      console.log('获取执行分页数===》', err);
    },
    refreshDeps:[connectionId]
  })

  const { run: runSetConnExecSqlRowNum, loading: rowNumLoading  } = useRequest(setConnExecSqlRowNum, {
   manual: true,
   onSuccess: () => {
    message.success(t('db.connection.conn.panel2.pageRows.success'));
   }
  })

  // 获取事务设置信息
  const {
    data: workData,
    loading: workLoading,
    run: queryConWorkConfig,
    refresh: refreshConWorkConfig,
  } = useRequest(getConnectionWorkConfig, {
    manual: true,
    formatResult: (data) => {
      const { variable, variable_value, value } = data
      if (operationType && operationType === "connectionSetting") {
        thisForm.setFieldsValue({ [variable]: value })
        thisForm.getFieldsValue()
      }
      return {
        ...data,
        value: variable_value === 'true' ? true : false,
      }
    },
  })

  // 获取连接池配置信息
  const {
    data: poolData,
    loading: poolLoading,
    run: queryConPoolConfig,
    refresh: refreshConPoolConfig,
  } = useRequest(optionType === "add" ? getConnectionPoolDefaultConfig : getConnectionPoolConfig, {
    manual: true,
    formatResult: (data) => {
      return data
        ?.map((item: any) => {
          if (item?.field === 'password') {
            item.value = item?.value ? atob(item?.value) : "";
          }
          return item
        })
        ?.map((item: any) => ({
          ...item,
          label: item?.label,
          value: item?.options
            ? item?.options?.find((e: any) => e?.key === item?.value)?.title
            : item?.value,
        }))
    },
  })

  useEffect(() => {
    if (connectionId && optionType !== "add") {
      queryConPoolConfig(connectionId)
      queryConWorkConfig(connectionId)
    }
    if (optionType === "add") {
      queryConPoolConfig(dataSourceType)
    }
  }, [connectionId, queryConPoolConfig, queryConWorkConfig, dataSourceType])

  useEffect(() => {
    if (poolData) {
      const initialValus: any = {}
      poolData?.forEach((i: any) => {
        initialValus[i?.field] = i?.value
      })

      thisForm.setFieldsValue(initialValus);
      // console.log(poolData, 'poolData');
    }
  }, [thisForm, poolData, workData])

  // 设置查询时分页行数
  const handleConnExecSqlRowNum = () => {
      const rowNum: number = Number(pageRows)
      const params = {
        connectionId,
        rowNum
      }
      runSetConnExecSqlRowNum(params)
      setPageRowIsEdit(false)
  }

  // 事务设置修改
  const handleWorkChange = (e: any) => {
    const value = e.target.value ? 'true' : 'false'
    const params: any = {
      connectionId,
      variable: workData?.variable,
      variable_value: value,
    }
    updateConnectionWorkConfig(params)
      .then(() => {
        message.success(t('db.connection.conn.panel2.workConfig.success'))
      })
      .catch((err: any) => {
        console.error('事务设置失败', err)
      })
  }

  // 连接池配置
  const handleConnectionPoolSave = () => {
    thisForm.validateFields().then(async (values: any) => {
      const params = {
        connectionId,
        userInputs: values,
      }

      updateConnectionPoolConfig(params)
        .then(() => {
          message.success(t('db.connection.conn.pool.setting.success'))
          refreshConPoolConfig()
          refreshConWorkConfig()
        })
        .catch((err: any) => {
          console.error('连接池配置失败', err)
        }).finally(() => {
          setPoolIsEdit(false)
        })
    })
  }

  const handleCancle = () => {
    thisForm.resetFields();
    refreshGetRowNum()
  }

  if (workLoading || poolLoading) {
    return <Spin spinning={workLoading || poolLoading}></Spin>
  }

  // 设置分页行数数字校验
  const handleChange = (e: any) => {
    const realEvent = e.nativeEvent
    const { value: inputValue } = realEvent?.target || {};
    setPageRows(inputValue);
  };


  /**
   * 查询时分页行数验证器
   *
   * @param value 分页行数
   * @returns 返回 Promise 对象，若验证通过则返回 resolved Promise，否则返回 rejected Promise
   */
  const pageRowsValidator = (value: string | undefined): Promise<void> => {
    if (!value) {
      // required会拦截
      return Promise.resolve()
    }
    const reg = /^\+?[1-9]\d*$/
    if (reg.test(value) === false) {
      return Promise.reject(new Error(t('db.connection.conn.panel2.rowNum.hint')))
    }
    if (Number(value) > 2000 || Number(value) < 100) {
      return Promise.reject(new Error(t('db.connection.conn.panel2.rowNum.hint2')))
    }
    return Promise.resolve()
  }

  const renderForm = () => {

    return (
      <div className={styles.connectSettingWrap}>
        <div className={styles.settingTitle}>{t('db.connection.conn.panel2.workConfig')}</div>
        {
          canEdit
            ? <Radio.Group
              defaultValue={workData?.value}
              className={styles.ml10}
              onChange={handleWorkChange}
            >
              <Radio value={true}>{t('db.connection.conn.panel2.workConfig.auto')}</Radio>
              <Radio value={false} disabled={hideTransactionSetting}>{t('db.connection.conn.panel2.workConfig.manual')}</Radio>
            </Radio.Group>
            : <Tooltip title={t('db.connection.noPerm', {roleNameList: roleNameList?.join(',') })}>
              <Radio.Group
                defaultValue={workData?.value}
                className={styles.ml10}
                disabled={true}
              >
                <Radio value={true}>{t('db.connection.conn.panel2.workConfig.auto')}</Radio>
                <Radio value={false}>{t('db.connection.conn.panel2.workConfig.manual')}</Radio>
              </Radio.Group>
            </Tooltip>
        }
        <div className={classnames(styles.settingTitle, styles.mt20)}>
          {t('db.connection.conn.panel2.pool')}
        </div>
        {!!poolData?.length && (
          <Form
            form={thisForm}
            labelCol={{ span: isLangEn ? 12 : 8 }}
            wrapperCol={{ span: 14 }}
          >
            <Row align='bottom' {...(isLangEn ? { gutter: 10 } : {})}>
              <Col span={14}>
                {poolData?.map((item: any, index: string) => (
                  <GenerateFormItem key={index} spec={{...item, onlyRead: !poolIsEdit}} labelWithTooltip={true} />
                ))}
              </Col>
              <Col span={10}>
                <Form.Item
                >
                  {
                    canEdit
                      ? <>
                          {
                            poolIsEdit
                            ? <>
                                <Button type="primary" onClick={handleConnectionPoolSave}>
                                  {t('common.btn.save')}
                                </Button>
                                <Button 
                                  className={styles.ml10} 
                                  onClick={()=>{
                                    handleCancle()
                                    setPoolIsEdit(false)
                                  }}
                                >
                                  {t('common.btn.cancel')}
                                </Button>
                              </>
                            : <span className='options' onClick={()=> setPoolIsEdit(true)}>{t('common.btn.edit')}</span>
                          }
                        </>
                      :
                      <Tooltip title={t('db.connection.noPerm', {roleNameList: roleNameList?.join(',') })}>
                        <span className='disabled'>{t('common.btn.edit')}</span>
                      </Tooltip>
                  }
                </Form.Item>
              </Col>
            </Row>
          </Form>
        )}
        {/* sql备份 */}
        {
          sqlBackupDT?.includes(dataSourceType.toLocaleLowerCase()) &&
          <ConnectionDataBackup
            connectionId={connectionId}
            dataSourceType={dataSourceType}
            canEdit={canEdit}
            permissionlist={permissionlist}
          />
        }
        <div className={classnames(styles.settingTitle, styles.mt20)}>
          {t('db.connection.conn.panel2.otherSetting')}
        </div>
        {/* 管理员修改 */}
        <div className={classnames(styles.mt20)}>
          <ChooseManage
            formProps={
              {
                labelCol: { span: isLangEn ? 6 :  4 },
                wrapperCol: { span: 14 },
              }
            }
            connectionId={connectionId}
            permissionlist={permissionlist}
            canEdit={canEdit}
            isRetest={connectionPool.filter((item: any) => item.field === "isAdminWithAllPerms")?.[0]?.value} //判断连接配置中是否要显示管理员权限二次确认
            setHasModifiedConnAdmin={() => setHasModifiedConnAdmin(!hasModifiedConnAdmin)}
          />
        </div>

        {/* 密钥 */}
        <div className={classnames(styles.mt20)}>
          <ConnectionSecretKeyPage
            connectionId={connectionId}
            permissionlist={permissionlist}
            canEdit={canEdit}
          />
        </div>
        <div>
          <Form
            labelCol={{ span: isLangEn ? 12 : 8 }}
            wrapperCol={{ span: 12 }}
            form={thisForm}
          >
            <Row {...(isLangEn ? { gutter: 10 } : {})}>
              <Col span={12}>
                <Form.Item
                  label={t('db.connection.conn.panel2.pageRows')}
                  name='pageRows'
                  rules={[
                    { required: true, message: t('db.connection.conn.panel2.pageRows.plac') },
                    { validator: (_, value) => pageRowsValidator(value) }
                  ]}
                >
                  {
                    pageRowIsEdit
                    ? <Input
                        value={pageRows}
                        onChange={handleChange}
                        placeholder={t('db.connection.conn.panel2.pageRows.plac')}
                      />
                    : <span>{pageRows}</span>
                  }
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item
                >
                  {
                    canEdit
                      ? <>
                          {
                            pageRowIsEdit
                            ? <>
                                <Button type="primary" onClick={handleConnExecSqlRowNum} loading={rowNumLoading}>
                                  {t('common.btn.save')}
                                </Button>
                                <Button 
                                  className={styles.ml10} 
                                  onClick={()=>{
                                    handleCancle()
                                    setPageRowIsEdit(false)
                                  }} 
                                  disabled={rowNumLoading} 
                                >
                                  {t('common.btn.cancel')}
                                </Button>
                              </>
                            : <span className='options' onClick={() => setPageRowIsEdit(true)}>{t('common.btn.edit')}</span>
                          }
                        </>
                      :
                        <Tooltip title={t('db.connection.noPerm', {roleNameList:roleNameList?.join(',')})}>
                          <span className='disabled'>{t('common.btn.edit')}</span>
                        </Tooltip>
                  }
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
        <div>
          <ChooseApproverGroup
            formProps={
              {
                labelCol: { span: isLangEn ? 6 :  4 },
                wrapperCol: { span: 7 },
              }
            }
            permissionlist={permissionlist}
            canEdit={canEdit && !isArchiveConnection}
            connectionId={connectionId}
            isRefresh={hasModifiedConnAdmin}
            onCreateApproverGroup={() => {
            
              dispatch(updateApproverGroupModalVisible(true));
              dispatch(updateCurEditApproverType('addConnection'));
            }}
          />
        </div>
      </div>
    )
  }

  // 连接概览-连接配置
  const renderFormItems = () => {
    return (
      <>
        <Form.Item
          label={t('db.connection.conn.panel2.workConfig')}
          name={'commitMode'}
          key={'commitMode'}
          labelCol={{ span: isLangEn ? 8 : 5}}
          wrapperCol={{ span: 19 }}
          rules={[{ required: true }]}
          initialValue={optionType === "add" ? false : workData?.value}
        >
          <Radio.Group disabled={isArchiveConnection}>
            <Radio value={true} >{t('db.connection.conn.panel2.workConfig.auto')}</Radio>
            <Radio value={false} >{t('db.connection.conn.panel2.workConfig.manual')}</Radio>
          </Radio.Group>
        </Form.Item>
        {!!poolData?.length && (
          <Row align='bottom'>
            <Col span={24}>
              {poolData?.map((item: any, index: string) => (
                <GenerateFormItem key={item?.field || index} spec={{ ...item, disabled: isArchiveConnection }} labelWithTooltip={true}
                  operationType="connectionConfig"
                />
              ))}
            </Col>
          </Row>
        )}
        {/* sql备份 */}
        {
          sqlBackupDT?.includes(dataSourceType.toLocaleLowerCase()) &&
          <ConnectionDataBackup
            connectionId={connectionId}
            dataSourceType={dataSourceType}
            operationType="connectionConfig"
            form={thisForm}
            isArchiveConnection={isArchiveConnection}
          />
        }
        {/* 管理员修改 */}
        <div className={classnames(styles.mt20)}>
          <ChooseManage
            formProps={
              {
                labelCol: { span: isLangEn ? 8 : 5 },
                wrapperCol: { span: 14 },
              }
            }
            connectionId={connectionId}
            operationType="connectionConfig"
            form={thisForm}
            isRetest={optionType === 'edit' && thisForm.getFieldValue('isAdminWithAllPerms')}
            isArchiveConnection={isArchiveConnection}
          />
        </div>
        <Form.Item
          label={t('db.connection.conn.panel2.pageRows')}
          name={'pageRows'}
          key={'pageRows'}
          labelCol={{ span: isLangEn ? 8 : 5 }}
          wrapperCol={{ span: 7 }}
          rules={[
            { required: true, message: t('db.connection.conn.panel2.pageRows.plac') },
            { validator: (_, value) => pageRowsValidator(value) }
          ]}
        >
          <Input placeholder={t('db.connection.conn.panel2.pageRows.plac')} disabled={isArchiveConnection} />
        </Form.Item >
        <ChooseApproverGroup
          formProps={
            {
              labelCol: { span: isLangEn ? 8 : 5 },
              wrapperCol: { span: 7 },
            }
          }
          connectionId={connectionId}
          operationType="connectionConfig"
          optionType={optionType}
          form={thisForm}
          canEdit={!isArchiveConnection}
          onCreateApproverGroup={() => {
            dispatch(updateApproverGroupModalVisible(true));
            dispatch(updateCurEditApproverType('addConnection'));
          }}
          onSaveSelectedApproverGroup = {(record: any) => {
            dispatch(updateCurrentApproverGroupInfo(record));
          }}
        />
      </>
    )
  }

  return (
    (operationType && operationType === "connectionSetting") ? renderFormItems() : renderForm()
  )
}

export default ConnectionSetting