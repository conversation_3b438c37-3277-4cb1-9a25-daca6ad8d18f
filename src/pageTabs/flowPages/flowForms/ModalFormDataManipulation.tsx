import React, { useEffect, useMemo, useRef, useState } from 'react'
import { useRequest, useSelector, } from 'src/hook'
import { Form, Select } from 'antd'
import {
  FormItemApplicant,
  FormItemReason,
  FormItemTitle,
  FormItemEffectiveTime,
} from '../flowFormItems'
import { FormFieldsDataManipulation } from 'src/types'
import { FlowModalForm } from './FlowModalForm'
import { getPointNodePathFiledValue } from 'src/util'
import { launchFlowApply, getDBTypeAndNodeTypeAuth } from 'src/api'
import { FormItemElements, FormItemMultiConectionApprover} from '../flowFormItems'
import { isEmpty } from 'lodash'
import { recursionGetRealNode } from '../utils'
import { useTranslation } from 'react-i18next'

export const ModalFormDataManipulation = React.memo(() => {
  const { userId: applyUserId, email } = useSelector(
    (state) => state.login.userInfo,
  )
  const { t } = useTranslation();
  const flowType = 'dataManipulation'
  //资源选择是否是schema以上层级
  const [selectedResourceInfo, setSelectedResourceInfo] = useState<any>();
  const [form] = Form.useForm()
  const { visibleFlows = [],trackingContext }: {visibleFlows: any; trackingContext?: any } = useSelector((state) => state.flowForms);
  const [defaultValue, setDefaultValue] = useState<any[]>([])
  const isfirstFlag = useRef<boolean>(true)
  const [allEleConnections, setAllEleConnections] = useState<any[]>([]);

  useEffect(() => {
    isfirstFlag.current = true

    if(!isEmpty(trackingContext?.fields?.elements)) {
      setDefaultValue(trackingContext?.fields?.elements)
    }
    form.setFieldsValue({
      elements: trackingContext?.fields?.elements,
      operationList: trackingContext?.fields?.operationList,
    })
  }, [trackingContext])

  //操作权限
  const { data: opePermissions = [], run: runGetOpePermissions } = useRequest(getDBTypeAndNodeTypeAuth, {
    manual: true,
    formatResult(res: any) {
      return res?.filter((i: any) => i?.canSelect)?.map((item: any) => ({ label: item?.operationName + '（' + item.operation + '）', value: item.operation }))
    }
  })

  const getPermissionsOptions = () => {
    const value = selectedResourceInfo?.nodePathValues;
    if (
      value?.length === 0 ||
      value?.filter((item: any) => value?.[0]?.connectionType === item?.connectionType)?.length !== value?.length
    ) {
      return null
    }
    return opePermissions
  }

  useEffect(() => {
    queryPermissions()
  }, [selectedResourceInfo])

  const queryPermissions = () => {
    if (!selectedResourceInfo?.connectionType || !selectedResourceInfo?.newNodeType) return
    const { connectionType, nodeType } = selectedResourceInfo
    // 调用接口，获取操作权限options
    runGetOpePermissions({
      dataSourceType: connectionType,
      objectType: nodeType === 'oracleUser' ? 'schema' : nodeType,
    })
  }

  const deepGetConnectionId = (children: any) => {
    const flatMapConnectionIds = children?.flatMap((e: any) => {
      if (['datasource', 'group'].includes(e?.nodeType)) {
        return deepGetConnectionId(e?.children || []);
      }
      //根据nodepathWithType 获取connectionId
      const connectionId = getPointNodePathFiledValue(e?.nodePathWithType, 'CONNECTION')
      return connectionId
    });
    return flatMapConnectionIds;
  }

  const connectionList = useMemo(() => {
    //组层级或数据源层级需要单独处理
    if (!selectedResourceInfo?.nodePathValues?.length || !allEleConnections?.length) return [];
    const flatMapConnectionIds = deepGetConnectionId(selectedResourceInfo.nodePathValues)
    //查找到connectionId对应的nodename
    const filteredConnectionIds = Array.from(new Set(flatMapConnectionIds));

    return filteredConnectionIds?.map((id: any) => {
      const curConnection = allEleConnections?.find(i => i?.connectionId === id);

      return {
        nodeName: curConnection?.nodeName,
        dataSourceType: curConnection?.dataSourceType,
        connectionId: Number(id)
      }
    })
  }, [JSON.stringify(selectedResourceInfo?.nodePathValues),])

  useEffect(() => {
    form.setFieldsValue({
      connAndAssigneeMapList: connectionList
    })
  }, [form, JSON.stringify(connectionList),visibleFlows?.includes(flowType)])

  return (
    <FlowModalForm<FormFieldsDataManipulation>
      type={flowType}
      name={`flow-${flowType}`}
      form={form}
      request={async (values) => {
        if (!applyUserId) return
        const {
          applyReason,
          elements,
          timeType,
          time,
          operationList,
          title,
          connAndAssigneeMapList = []
        } = values
        const {
          connectionId,
          connectionType: dataSourceType,
          nodeType: nType,
        } = elements?.[0] || {}
        let beginTime: any = undefined
        let endTime: any = undefined
        if (timeType !== 'forever' && time && Array.isArray(time)) {
          beginTime = time?.[0].format('YYYY-MM-DD HH:mm:ss')
          endTime = time?.[1].format('YYYY-MM-DD HH:mm:ss')
        }
        let eleRealNodes: any[] = []
        elements.map((ele: any) => {
          eleRealNodes = eleRealNodes.concat(recursionGetRealNode(ele))
        })
        const nodePathWithTypeList = eleRealNodes.map(({ nodePathWithType }) => nodePathWithType)
        const nodePathList = eleRealNodes.map(({ nodePath }) => nodePath)
        const nodeType = ['datasource', 'group'].includes(nType) ? 'connection' : nType
        return launchFlowApply({
          applyUserId,
          email,
          title,
          applyReason,
          remark: applyReason,
          flowType,
          connectionId,
          dataSourceType,
          nodePathList,
          nodePathWithTypeList,
          nodeType,
          operationList,
          beginTime,
          endTime,
          source: 1,
          ...(connAndAssigneeMapList?.length ? { connAndAssigneeMapList } : {})
        })
      }}
    >
      
      <FormItemApplicant />
      <FormItemTitle />
      {/* 数据库元素 */}
      <FormItemElements
        form={form}
        setSelectedResourceInfo={setSelectedResourceInfo}
        defaultValue={defaultValue}
        tooltipTitle={t('flow_shared_permissions_warning')}
        callbackConnections={setAllEleConnections}
      />
      {/* 操作权限 */}
      <Form.Item
        label={t('flow_operation_permissions')}
        name={'operationList'}
        dependencies={['elements']}
        rules={[
          { required: true, message: t('flow_select_operation_permission') },
        ]}
      >
        <Select
          placeholder={t('flow_select_operation_permission')}
          options={getPermissionsOptions()}
          mode={'multiple'}
          allowClear
          maxTagCount={3}
        />
      </Form.Item>
      <FormItemEffectiveTime
        form={form}
        effectiveTimeRequired={true}
        effectiveTimeKey="timeType"
        timeKey="time"
      />
      <FormItemReason />
      <FormItemMultiConectionApprover
        flowType={'FAT'}
        isRender={visibleFlows?.includes(flowType)}
        isMultiConnection={true}
      />
    </FlowModalForm>
  )
})
