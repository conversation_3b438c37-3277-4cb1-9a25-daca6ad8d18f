import React, { useEffect, useState } from "react";
import { Form, DatePicker, Select } from "antd";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { useSelector } from 'src/hook'
import { disabledDate, disabledTime } from "src/util";
import { 
  FormattedTimeDataItem , 
  isForeverKey,
  isCustomKey
}  from 'src/pageTabs/settingPage/settingCards/authConfigCard/utils';

const { RangePicker } = DatePicker;
interface IProps {
  form: any;
  effectiveTimeKey: any;
  timeKey: any;
  effectiveTimeRequired?: boolean;
  formLayout?: any;
  callback?: () => void;
  defabultEffectiveTime?:  any  //默认值
}
 
export const FormItemEffectiveTime = (props: IProps) => {
  const { form, effectiveTimeKey,defabultEffectiveTime, timeKey, effectiveTimeRequired, callback, formLayout = null } = props
  
  const customAuthValidOptions = useSelector(state => state.login.customAuthorizationPeriod)

  const [selectedTimeOption, setSelectedTimeOption] = useState<FormattedTimeDataItem | null>(null);

  const { t } = useTranslation();

  useEffect(() => {
    if (defabultEffectiveTime) {
      //如果有默认值 则只需要控制展示日期选择框即可
      //@ts-ignore
      setSelectedTimeOption({ timeValueUnit: defabultEffectiveTime })
    }
  }, [defabultEffectiveTime])
  const getDefaultTime = (timeType: string, option: FormattedTimeDataItem) => {
  
    //设置当前选中时间段信息
    setSelectedTimeOption(option);

    const { timeValue = 0, timeValueUnit = 'day' } = option;
    
    //如果为自定义时间，则不设置默认值
  
    if (isCustomKey(timeValueUnit)) {
      //处理多个时间段的情况
      if (Array.isArray(timeKey)) {
        form.setFieldsValue({
          [timeKey[0]]: {
            [timeKey[1]]: null
          }
        })
      }else {
        form.setFieldsValue({ [timeKey]: null });
      }
      return;
    }
    //非自定义时间 默认单位是天
    let momentUnit: any = 'd' ;
    if (timeValueUnit === 'hour') {
      momentUnit = 'hour'
    } 
    //时间段设置已排为0情况
    if (timeValue !== 0) {
      if (Array.isArray(timeKey)) {
        form.setFieldsValue({
          [timeKey[0]]: {
            [timeKey[1]]: [moment(), moment().add(timeValue, momentUnit)]
          }
        })
      } else {
        form.setFieldsValue({
          [timeKey]: [moment(), moment().add(timeValue, momentUnit)]
        })
      }
    }
  }

  return (
    <Form.Item noStyle>
      <Form.Item label={t('db.auth.connectSetting.tab.users.effectiveTime')} name={effectiveTimeKey}
        rules={effectiveTimeRequired? [{ required: true, message: t('db.auth.connectSetting.tab.users.effectiveTime.plac') }] : []}
        {...formLayout}
      >
        <Select
          style={{ width: '100%' }}
          placeholder={t('db.auth.connectSetting.tab.users.effectiveTime.plac')}
          options={customAuthValidOptions}
          onChange={(e, option) => getDefaultTime(e as string, option as FormattedTimeDataItem)}
          onFocus={() => {callback && callback()}}
        />
      </Form.Item>
      <Form.Item
        noStyle
        dependencies={[effectiveTimeKey]}
      >
        {({ getFieldValue }) => {
          const effectiveTime = getFieldValue(effectiveTimeKey);
         
          const isShowTimeRange = effectiveTime && selectedTimeOption && !isForeverKey(selectedTimeOption?.timeValueUnit);

        return isShowTimeRange ? (
            <Form.Item
              label={t('db.auth.connectSetting.tab.users.timeRange')}
              name={timeKey}
              {...formLayout}
              rules={[
                { required: true },
                {
                  validator(_rule, value, cb) {
                    const s = moment(value?.[0]).valueOf();
                    const e = moment(value?.[1]).valueOf();
                    if (!s || !e) {
                      cb(t('db.auth.connectSetting.tab.users.timeRange.hint'));
                    }
                    if (e < moment().valueOf()) {
                      cb(t('db.auth.connectSetting.tab.users.timeRange.hint2'));
                    } else if (s > e) {
                      cb(t('db.auth.connectSetting.tab.users.timeRange.hint3'));
                    }
                    cb();
                  },
                },
              ]}
            >
              <RangePicker
                disabledDate={disabledDate}
                disabledTime={disabledTime}
                style={{width: '100%'}}
                showTime={{
                  hideDisabledOptions: true,
                }}
                format={t('common.timeFormat')}
                onChange={(values: any) => {
         
                  if (!isForeverKey(selectedTimeOption?.timeValueUnit)) {
                    //获取自定义所在的value
                    const customOption = customAuthValidOptions?.find(time => isCustomKey(time?.timeValueUnit))

                    if (Array.isArray(timeKey)) {
                      form.setFieldsValue({
                        [timeKey[0]]: {
                          [effectiveTimeKey[1]]: customOption?.value
                        }
                      })
                    
                    } else {
                      form.setFieldsValue({ [effectiveTimeKey]: customOption?.value });
                    }
                  }
                }}
                onFocus={() => {callback && callback()}}
              />
            </Form.Item>
          ) : null
        }}
      </Form.Item>
    </Form.Item>
  );
};
