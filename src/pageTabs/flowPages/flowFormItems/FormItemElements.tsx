import React from 'react'
import { Form, Tooltip } from 'antd'
import { FormFieldElementEntity } from 'src/types'
import ResourceTreeSelector from './ResourceTreeSelector';
import { cloneDeep } from 'lodash';
import { FormInstance } from 'antd/lib/form';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

interface IFormItemElement {
  form: FormInstance<any>
  setSelectedResourceInfo: React.Dispatch<any>
  setElementsValues?: React.Dispatch<any>
  defaultValue: any
  endSchema?: boolean
  tooltipTitle?: JSX.Element | string
  callbackConnections: (connection?: any) => void
}

export const FormItemElements = (props: IFormItemElement) => {
  const { t } = useTranslation()
  const { form, setSelectedResourceInfo, setElementsValues, defaultValue = {}, endSchema = false, tooltipTitle, callbackConnections } = props;
  const schemaAndAboveNodeTypes = ['datasource', 'connection', 'group', 'database', 'schema', 'oracleUser'];

  return <Form.Item
    label={
      tooltipTitle ?
        <div>
          {t('flow_database_ele')}
          <Tooltip title={tooltipTitle}><ExclamationCircleOutlined style={{ color: '#878787', fontSize: '14px', marginLeft: '4px' }} /></Tooltip>
        </div> :
        t('flow_database_ele')
    }
    name="elements"
    required
    rules={[
      {
        validator: (_, value: FormFieldElementEntity[], callback: (p?: string) => void) => {
          if (!value?.length) {
            form.setFieldsValue({ 'operationList': [] })
            callback(t('flow_select_db_ele'))
          }

          const ruleObj = value[0];
          //多选时 最底层nodeType
          let nodeTypeIndex = schemaAndAboveNodeTypes.indexOf(ruleObj?.nodeType);
          //schema以上
          const isSchemaAndAbove = ruleObj?.nodeType && schemaAndAboveNodeTypes.includes(ruleObj.nodeType);

          for (const el of value) {
            const curElType = el?.nodeType && schemaAndAboveNodeTypes.includes(el.nodeType);
            const curElNodeTypeIndex = el?.nodeType && schemaAndAboveNodeTypes.indexOf(el.nodeType);

            nodeTypeIndex = curElNodeTypeIndex > nodeTypeIndex ? curElNodeTypeIndex : nodeTypeIndex;

            if (el.connectionType !== ruleObj.connectionType) {
              form.setFieldsValue({ 'operationList': [] })
              callback(t('flow_select_same_data_source_ele'))
            }

            if (isSchemaAndAbove !== curElType) {
              form.setFieldsValue({ 'operationList': [] })
              callback(t('flow_select_same_level_ele'))
            }
            //schema以下
            if (!isSchemaAndAbove && el.nodeType !== ruleObj.nodeType) {
              form.setFieldsValue({ 'operationList': [] })
              return Promise.reject('flow_select_same_type_elements')
            }
          }
          setElementsValues?.(cloneDeep(value))

          // 手动设置nodeType
          const nodeType = ruleObj?.schemaFlag ? 'schema' 
          : isSchemaAndAbove ? schemaAndAboveNodeTypes[nodeTypeIndex || 0] 
          : ruleObj?.nodeType

          setSelectedResourceInfo({
            nodePathValues: [...value],
            connectionType: value?.[0]?.connectionType,
            nodeType: nodeType,
            nodePathWithType: value?.[0]?.nodePathWithType,
            permissionList: value?.[0]?.sdt?.permissionList,
            newNodeType: value?.[0]?.newNodeType,
          });
          callback()
        },
      }]}
  >
    <ResourceTreeSelector defaultValue={defaultValue} endSchema={endSchema} form={form} callbackConnections={callbackConnections}/>
  </Form.Item>
}
