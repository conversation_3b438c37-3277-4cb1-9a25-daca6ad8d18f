import React, { PropsWithChildren, useEffect, useMemo } from 'react'
import {
  useSelector,
  useDispatch,
  useRequest,
  useSdtNodeChildren,
  usePaginatedSdtNodeChildren,
} from 'src/hook'
import { Row, Col, message, TreeSelect, Spin } from 'antd'
import { SelectMore as Select } from 'src/components/SelectMore'
import {
  setConnectionSession,
  PaneInfo,
  updateConnectionSession,
} from '../../queryTabsSlice'
import { getExecutedSql, NodeEntity } from 'src/api'
import { useTranslation } from 'react-i18next'

interface SelectorProps {
  paneInfo: PaneInfo
  clearModelDec: () => void
  oneLibraryAndOneConnection?: boolean
}

// antd select bug 受控组件 value 为undefined 时 select 其他选项value 明明为undefined 但视图却改变了 使用null 作为转换值
const transformValueUndefined2Null = (value: any) => value === undefined ? null : value

/**
 * session related controllers in editor toolbar
 */
export const MonacoToolbarSessionSelectors = React.memo(
  ({ paneInfo, clearModelDec, oneLibraryAndOneConnection }: PropsWithChildren<SelectorProps>) => {
    const { t } = useTranslation()
    const dispatch = useDispatch()
    const {
      key,
      connectionId,
      databaseName,
      schemaName,
      pending: executionPending,
      plSql,
      tSql,
    } = paneInfo

    const dataSourceDescInfo = useSelector((S) => S.dataSource.dataSourceMap)

    // TODO: 多余的接口调用，应该从 toolbar 上事务相关部分获取
    /* 切库或者切表之前检查是否存在事务 */
    const { run: getIsInTransaction } = useRequest(
      () => {
        if (!connectionId || !databaseName) {
          return Promise.resolve([])
        }
        return getExecutedSql({
          tabKey: key,
          connectionId,
          databaseName,
        } as any)
      },
      {
        manual: true,
        formatResult: (data) => data && data[0],
      },
    )
    // toolbarConnections
    const { toolbarConnections, loadingConnections, toolbarConnectionsWithGroup } = useSelector(
      (state) => state.editor,
    )
    // connection options and selected value
    const selectedConnection = useMemo(
      () =>
        toolbarConnections?.find((node) => node.connectionId === connectionId),
      [connectionId, toolbarConnections],
    )

    const dataSourceLevel =
      dataSourceDescInfo[selectedConnection?.connectionType || 'MySQL']
        ?.dataSourceLevel || 2

    const { data: toolbarDatabases, loading: loadingDatabases, hasMore: hasMoreDatabases, loadMore: loadMoreDatabases } =
      usePaginatedSdtNodeChildren(selectedConnection)

    const targetDatabase = useMemo(
      () =>
        toolbarDatabases?.find((node) => {
          if (['DamengDB', 'DB2'].includes(node.connectionType)) {
            return node.nodeName === schemaName
          } else {
            return node.nodeName === databaseName
          }
        }),
      [databaseName, toolbarDatabases, schemaName],
    )

    const databaseOptions = useMemo(() => {
      return toolbarDatabases?.map(({ nodeName, nodePath }) => ({
        label: nodeName,
        value: nodeName,
        key: nodePath, // 使用 nodePath 作为唯一 key
      })) || []
    }, [toolbarDatabases])

    const onSelectConnection = async(path: string | number ,option: any) => {
      
      const isInTransaction = await getIsInTransaction()
      if (isInTransaction) {
        message.error(t('sdo_in_transaction_error_txt'))
        return
      }

      if (option) {
        const { connectionId, connectionType } = option
        // dispatch(fetchTabColor(key,connectionId))
        dispatch(
          setConnectionSession({
            key,
            paneInfo: {
              connectionId,
              connectionType,
              databaseName: '',
              schemaName: '',
            },
          }),
        )
      }
      // 切换数据库连接也需要清空之前的错误标记
      clearModelDec()
    }

    return (
      <Row gutter={[8, 4]}>
        <Col>
            <TreeSelect
              filterTreeNode={(inputV: string, treeNode: any) => {
                if (!inputV) return true
                if (treeNode.nodeName && (treeNode.nodeName?.toLowerCase()).includes(inputV.toLowerCase()))
                  return true
                return false
              }}
              // treeNodeLabelProp={"alias"|| "nodeName"}
              style={{ width: 160 }}
              dropdownStyle={{ width: 'max-content' }}
              dropdownMatchSelectWidth={false}
              value={transformValueUndefined2Null(selectedConnection?.nodePath)}
              treeData={toolbarConnectionsWithGroup as any}
              placeholder={t('sdo_select_connection')}
              size="small"
              showSearch
              allowClear
              treeIcon
              onSelect={onSelectConnection}
              loading={loadingConnections}
              disabled={executionPending || loadingDatabases || plSql || tSql}
              showArrow={!plSql && !tSql}
              onClear={() => {
                 //清空connetion
                  dispatch(
                    setConnectionSession({
                      key,
                      paneInfo: {
                        //@ts-ignore
                        connectionId: undefined,
                        //@ts-ignore
                        connectionType: undefined,
                        databaseName: '',
                        schemaName: '',
                        tabColor:undefined
                      },
                    }),
                  )
              }}
            />
        </Col>
        <Col>
          {selectedConnection && (
            <Select
              options={databaseOptions}
              style={{ width: 160 }}
              allowClear
              placeholder={['DamengDB', 'DB2'].includes(selectedConnection.connectionType) ? `${t('sdo_select')} schema` : t('sdo_select_db_placeholder')}
              value={transformValueUndefined2Null(targetDatabase?.nodeName)}
              onChange={async (databaseName) => {
                const isInTransaction = await getIsInTransaction()
                // 一库一连接为true时，databases不允许切换
                // http://share.bintools.cn:8080/pages/viewpage.action?pageId=2176650
                if (isInTransaction && oneLibraryAndOneConnection) {
                  message.error(t('sdo_in_transaction_error_txt'))
                  return
                }
                //清空databaseName 除了清除dbname 还要清除schemaname
                if (databaseName === undefined) {
                  dispatch(
                    updateConnectionSession({
                      key,
                      paneInfo: {
                        databaseName: '',
                        schemaName: ''
                      },
                    }),
                  )
                }
                // 设置查询窗口上下文
                const database = toolbarDatabases?.find(
                  (node) => node.nodeName === databaseName,
                )
                if (database) {
                  const {
                    connectionId,
                    connectionType,
                    nodeName: databaseName,
                  } = database
                  let paneInfo = {
                    connectionId,
                    connectionType,
                    databaseName,
                    schemaName: '',
                  }
                  if (['DamengDB', 'DB2'].includes(connectionType)) {
                    paneInfo = {
                      connectionId,
                      connectionType,
                      databaseName: '',
                      schemaName: databaseName,
                    }
                  }

                  // dispatch(fetchTabColor(key,connectionId))
                  dispatch(
                    setConnectionSession({
                      key,
                      paneInfo,
                    }),
                  )
                }
                clearModelDec()
              }}
              size="small"
              showSearch
              filterOption={(inputValue: string, option: any) => {
                if (!inputValue) return true
                if (option.label && (option.label?.toLowerCase()).includes(inputValue.toLowerCase()))
                  return true
                return false
              }}
              loading={loadingDatabases}
              disabled={executionPending}
              dropdownRender={(menu) => (
                <div>
                  {menu}
                  {hasMoreDatabases && (
                    <div
                      style={{
                        padding: '8px 12px',
                        borderTop: '1px solid #f0f0f0',
                        cursor: 'pointer',
                        textAlign: 'center',
                        color: '#1890ff',
                      }}
                      onClick={async (e) => {
                        console.log('Load more clicked')
                        e.stopPropagation()
                        e.preventDefault()
                        await loadMoreDatabases()
                      }}
                    >
                      <Spin spinning={loadingDatabases} size="small">
                        {t('sdo_load_more')}...
                      </Spin>
                    </div>
                  )}
                </div>
              )}
            />
          )}
        </Col>
        {/* schema selector */}
        <Col>
          {dataSourceLevel === 3 && (
            <SchemaSelectRenderer
              database={targetDatabase}
              getIsInTransaction={getIsInTransaction}
              paneInfo={paneInfo}
              clearModelDec={clearModelDec}
            />
          )}
        </Col>
      </Row>
    )
  },
)

interface SchemaSelectRendererProps extends SelectorProps {
  database?: NodeEntity
  getIsInTransaction?: () => Promise<string>
}

function SchemaSelectRenderer({
  database,
  paneInfo,
  clearModelDec,
  getIsInTransaction,
}: SchemaSelectRendererProps) {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  const { key, schemaName, pending } = paneInfo

  const { data: schemas, loading } = useSdtNodeChildren(database)
  const schemaOptions = schemas?.map(({ nodeName }) => ({
    label: nodeName,
    value: nodeName,
  }))
  const targetSchema = useMemo(
    () =>
      schemas?.find((node) => {
        return node.nodeName === schemaName
      }),
    [schemaName, schemas],
  )


  useEffect(() => {
    if (!database || !targetSchema || pending) return
    const { connectionId, connectionType, nodeName: schemaName } = targetSchema
    const databaseName = database.nodeName
    const dataSourceType = connectionType
    // dispatch(
    //   changeDatabase({
    //     tabKey: key,
    //     connectionId,
    //     dataSourceType,
    //     databaseName,
    //     operatingObject: getOperatingObject(
    //       { databaseName, schemaName },
    //       dataSourceType,
    //     ),
    //   }),
    // )
    //   .unwrap()
    //   .then(() => {
        dispatch(
          setConnectionSession({
            key,
            paneInfo: {
              connectionId,
              connectionType,
              databaseName,
              schemaName,
            },
          }),
        )
      // })
      // .catch(() => {
      //   dispatch(
      //     setConnectionSession({
      //       key,
      //       paneInfo: {
      //         connectionId,
      //         connectionType,
      //         databaseName,
      //         schemaName: '',
      //       },
      //     }),
      //   )
      // })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [database, dispatch, key, targetSchema])

  return (
    <Select
      options={schemaOptions}
      style={{ width: 160 }}
      allowClear
      placeholder={`${t('sdo_select')} schema`}
      value={transformValueUndefined2Null(targetSchema?.nodeName)}
      onChange={async (schemaName) => {

        // 存在事务时，schema允许切换
        // http://share.bintools.cn:8080/pages/viewpage.action?pageId=2176650
        // const isInTransaction = await getIsInTransaction?.()
        // if (isInTransaction) {
        //   message.error(t('sdo_in_transaction_error_txt'))
        //   return
        // }
        // 清空schemaname
        if (schemaName === undefined) {
          dispatch(
            updateConnectionSession({
              key,
              paneInfo: {
                schemaName,
              },
            }),
          )
        }
        // 设置查询窗口上下文
        const schema = schemas?.find((node) => node.nodeName === schemaName)
        if (schema) {
          const { connectionId, connectionType, nodeName: schemaName } = schema
          const databaseName = database?.nodeName || ''
          dispatch(
            setConnectionSession({
              key,
              paneInfo: {
                connectionId,
                connectionType,
                databaseName,
                schemaName,
              },
            }),
          )
        }
        clearModelDec()
      }}
      size="small"
      showSearch
      filterOption={(inputValue: string, option: any) => {
        if (!inputValue) return true
        if (option.label && (option.label?.toLowerCase()).includes(inputValue.toLowerCase()))
          return true
        return false
      }}
      loading={loading}
      disabled={pending}
    />
  )
}
