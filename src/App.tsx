/*
 * @Author: yang<PERSON><PERSON> <EMAIL>
 * @Date: 2023-09-19 13:55:26
 * @LastEditors: yangchao <EMAIL>
 * @LastEditTime: 2023-09-25 13:39:29
 * @FilePath: /cq-enterprise-frontend/src/App.tsx
 * @Description: 
 */
import React, { Suspense, useEffect, useRef } from 'react'
import { useSelector, useDispatch, useTick, useRequest } from 'src/hook'
import { isEqual } from 'lodash'
import { Router, Route, Redirect, Switch } from 'react-router-dom'
import { ConfigProvider, Modal, Spin } from 'antd'
// import { PasswordReset } from './appPages/passwordReset'
import { history } from 'src/util'
import zhCN from 'antd/es/locale/zh_CN'; // antd 中文语言包
import enUS from 'antd/es/locale/en_US'; // antd 英文语言包
import "src/assets/js/iconfont.js";  // 历史图标库内容
import 'src/assets/js/iconfont_enterprise.js'
import "src/assets/js/iconfont_latest.js"  //需要更新官方图标库时候直接替换内容 无需再新增
import 'resize-observer-polyfill'
import './styles/index.scss'
import './styles/theme.less'
import Login, { WaitLogin, WaitOauthLogin } from './appPages/login'
import { setAllFailedCountConnectionIds, setRedirectUrl } from './appPages/login/loginSlice'
import { NoAccess } from './appPages/403'
import { getUserPollingInfo } from './api'
import hoc from './hoc';
import { showModal } from './store/extraSlice/modalVisibleSlice'
import i18next from 'i18next';
import { CURRENT_LANGUAGE } from 'src/util/getCurrentLanguage'
import { setMessages } from 'src/store/extraSlice/useMesSlice'

const Main = React.lazy(() => import('./appPages/main'))

// 独立的登出管理组件 - 避免影响其他组件的重新渲染
const LogoutManager = React.memo(() => {
  useTick(); // 只在这个组件中使用，重新渲染不会影响其他组件
  return null; // 不渲染任何内容
});

export const ViewModules = () => {
  const { isLoggedIn, hasIpAccess, userInfo, allFailedCountConnectionIds } = useSelector((state) => state.login)
  const { userId='' } = userInfo || {}
  
  const dispatch = useDispatch()
  const pathname = window.location.pathname
  
  // 使用 ref 来保存最新的 allFailedCountConnectionIds，避免闭包问题
  const allFailedCountConnectionIdsRef = useRef(allFailedCountConnectionIds)
  
  useEffect(() => {
    allFailedCountConnectionIdsRef.current = allFailedCountConnectionIds
  }, [allFailedCountConnectionIds])

  // 多个轮询接口合并成一个
  const { run, cancel } = useRequest(getUserPollingInfo, {
    manual: true,
    pollingInterval: 5000,
    onSuccess: (res: any) => {
      const { unreadMessageByUserId, detectionSingleDeviceLogin, allFailedCountConnectionIds=[] } = res ?? {};
      // 单设备登录互踢
      const offline = detectionSingleDeviceLogin?.offline
      if (offline) {
        dispatch(showModal('ConfirmLogoutModal'));
      }
      // 错误连接次数过多 connecionId 列表
      // 优化：只有在数据真正发生变化时才 dispatch，避免不必要的重渲染
      if (!isEqual(allFailedCountConnectionIdsRef.current, allFailedCountConnectionIds)) {
        dispatch(setAllFailedCountConnectionIds(allFailedCountConnectionIds));
      }
      //消息通知改造后，字段有所改动，保持原来使用字段
      const formattedData =  unreadMessageByUserId?.data?.map((item: any) => {
        const { notificationVoExtra = {}, extraAttrsJson = {}, businessId, category, senderId, receiverId } = item
        return ({
          ...item,
          ...notificationVoExtra,
          ...extraAttrsJson,
          alarmType: extraAttrsJson?.alarmType,
          flowTaskId: extraAttrsJson?.taskId,
          param: extraAttrsJson?.alarmParam || {},
          applyId: businessId,
          msgId: businessId,
          msgType: category,
          sender: senderId,
          userId: [receiverId]  //原数据结构返回为数组，此处改为字符串
        })
      } )
      // 未读消息
      const msgInfo = {
        userId,
        messages: formattedData,
        num: unreadMessageByUserId?.total,
      } 
      dispatch(setMessages(msgInfo))
    }
  });

  useEffect(() => {
    if (
      pathname !== '/login' &&
      pathname !== '/' &&
      pathname !== '/waitlogin' &&
      pathname !== '/403'
    ) {
      dispatch(setRedirectUrl(encodeURIComponent(pathname)))
    }
  }, [pathname, dispatch])

  useEffect(() => {
    if (isLoggedIn) {
      run()
    }else {
      cancel()
      // 非登录状态清除审计概览缓存数据
      localStorage.removeItem('chartsDisplayList')
      localStorage.removeItem('chartsDisplayType')
      // 登录超时（未登录）清除所有弹框状态
      Modal.destroyAll()
    }
  }, [isLoggedIn])

  const fallbackLoading = (
    <div style={{ cursor: 'wait' }}>
      <Spin spinning style={{ color: '#03204a' }} tip={i18next.t("cloudQueryLoading")}>
        <div
          style={{
            width: '100vw',
            height: '100vh',
            backgroundColor: '#3f84e9',
          }}
        ></div>
      </Spin>
    </div>
  )

  const getNoAccessRoute = () => {
    if(!hasIpAccess){
      return <NoAccess />
    }
    if(!isLoggedIn){
      return <Redirect to="/login" />
    }
    return (
      <>
        {/* 登出管理器 - 独立组件，不影响其他组件渲染 */}
        <LogoutManager />
        <Suspense fallback={fallbackLoading}>
          <Main />
        </Suspense>
      </>
    )
  }
 
  return (
    <ConfigProvider locale={CURRENT_LANGUAGE === 'en' ? enUS : zhCN}>
      <Router history={history}>
        <Switch>
          <Route exact path="/login">
            {isLoggedIn ? <Redirect to="/system_home" /> : <Login />}
          </Route>
          <Route path="/waitlogin" key="/waitlogin">
            <WaitLogin />
          </Route>
          <Route path="/waitOauthLogin" key="/waitOauthLogin">
            <WaitOauthLogin />
          </Route>
          {/* <Route path="/password_reset">
            <PasswordReset />
          </Route> */}
          <Route>
            {getNoAccessRoute()}
          </Route>
        </Switch>
      </Router>
    </ConfigProvider>
  );
}

export default hoc(ViewModules)
