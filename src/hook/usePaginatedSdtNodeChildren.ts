import { useState, useMemo, useEffect, useCallback } from 'react'
import { useSelector, useDispatch } from 'src/hook'
import { fetchTreeNodeChildren } from 'src/pageTabs/queryPage/sdt'
import { getUserConfig } from 'src/api'
import type { NodeEntity } from 'src/types'

interface PaginatedNodeData {
  data: NodeEntity[]
  hasMore: boolean
  loading: boolean
  loadMore: () => Promise<void>
}

export const usePaginatedSdtNodeChildren = (parent?: NodeEntity): PaginatedNodeData => {
  const dispatch = useDispatch()
  const treeNodeChildrenMap = useSelector(
    (state) => state.sdt.treeNodeChildrenMap,
  )
  const groupByType = useSelector(
    (state) => state.sdt.groupByType,
  )
  
  const [loading, setLoading] = useState<boolean>(false)
  const [loadingMore, setLoadingMore] = useState<boolean>(false)
  const [pageNo, setPageNo] = useState<number>(1)
  const [pageSize, setPageSize] = useState<number>(100) // 默认值
  const [allData, setAllData] = useState<NodeEntity[]>([])
  const [hasMore, setHasMore] = useState<boolean>(false)

  // 获取用户配置的分页大小
  useEffect(() => {
    const fetchUserConfig = async () => {
      try {
        const { sdtPageSize } = await getUserConfig()
        setPageSize(sdtPageSize ?? 100)
      } catch (error) {
        console.error('Failed to get user config:', error)
      }
    }
    fetchUserConfig()
  }, [])

  // 获取缓存的数据
  const cachedData = useMemo(() => {
    if (parent) {
      const memo = treeNodeChildrenMap[parent.nodePath]
      return memo?.filter(({ switchable }) => switchable !== false) || []
    }
    return []
  }, [parent, treeNodeChildrenMap])

  // 加载数据的函数
  const loadData = useCallback(async (page: number, isLoadMore: boolean = false) => {
    if (!parent) return

    if (isLoadMore) {
      setLoadingMore(true)
    } else {
      setLoading(true)
    }

    try {
      const params = {
        ...parent,
        groupByType,
        isSdtNode: false,
        pageSize,
        pageNo: page,
        notMemorize: true, // 分页数据不缓存
        startPageNum: page,
      }

      const result = await dispatch(fetchTreeNodeChildren(params)).unwrap()
      const newData = result?.data || []

      if (isLoadMore) {
        // 加载更多时追加数据，去重处理
        setAllData(prev => {
          const existingPaths = new Set(prev.map(item => item.nodePath))
          const uniqueNewData = newData.filter(item => !existingPaths.has(item.nodePath))
          return [...prev, ...uniqueNewData]
        })
      } else {
        // 首次加载时替换数据
        setAllData(newData)
      }

      // 判断是否还有更多数据
      setHasMore(newData.length >= pageSize)
      
    } catch (error) {
      console.error('Failed to load database list:', error)
    } finally {
      setLoading(false)
      setLoadingMore(false)
    }
  }, [parent, dispatch, groupByType, pageSize])

  // 加载更多数据
  const loadMore = useCallback(async () => {
    if (loadingMore || !hasMore) return
    
    const nextPage = pageNo + 1
    setPageNo(nextPage)
    await loadData(nextPage, true)
  }, [loadData, pageNo, loadingMore, hasMore])

  // 初始加载
  useEffect(() => {
    if (parent && pageSize > 0) {
      // 如果有缓存数据且数量小于分页大小，直接使用缓存
      if (cachedData.length > 0 && cachedData.length < pageSize) {
        setAllData(cachedData)
        setHasMore(false)
        setPageNo(1)
      } else {
        // 否则重新加载第一页
        setPageNo(1)
        loadData(1, false)
      }
    }
  }, [parent, pageSize, cachedData.length, loadData])

  // 如果有缓存数据且数量小于分页大小，使用缓存数据
  const finalData = useMemo(() => {
    if (cachedData.length > 0 && cachedData.length < pageSize) {
      return cachedData
    }
    return allData
  }, [cachedData, allData, pageSize])

  const finalHasMore = useMemo(() => {
    if (cachedData.length > 0 && cachedData.length < pageSize) {
      return false
    }
    return hasMore
  }, [cachedData.length, pageSize, hasMore])

  const finalLoading = useMemo(() => {
    if (cachedData.length > 0 && cachedData.length < pageSize) {
      return false
    }
    return loading
  }, [cachedData.length, pageSize, loading])

  return {
    data: finalData,
    hasMore: finalHasMore,
    loading: finalLoading || loadingMore,
    loadMore,
  }
}
